## Check that successors of a basic block with jump table are generated
## in the same order as they appear in the input code.

RUN: %clang %cflags %S/Inputs/jump-table-pic.s -o %t.exe -Wl,-q
RUN: llvm-bolt %t.exe --strict --print-cfg --print-only=main -o %t.null \
RUN:   | FileCheck %s

CHECK:      BB Layout   : {{.*, .*, .*,}} [[BB4to6:.*, .*, .*]]

## Check that successors appear in the order matching the input layout.
CHECK:      jmpq    *%rax # JUMPTABLE
CHECK-NEXT: Successors: [[BB4to6]]
