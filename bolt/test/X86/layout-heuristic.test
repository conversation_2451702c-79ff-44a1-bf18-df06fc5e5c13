## Checks that llvm-bolt is able to read data generated by perf2bolt,  update the
## CFG edges accordingly with absolute number of branches and mispredictions,
## infer fallthrough branch info and reorder basic blocks using a greedy
## heuristic,  or find the optimal solution if the function is small enough.
## Also checks that llvm-bolt disassembler and CFG builder is working properly.

RUN: yaml2obj %p/Inputs/blarge.yaml &> %t.exe
RUN: llvm-bolt %t.exe -o %t.null --data %p/Inputs/blarge.fdata \
RUN:   --reorder-blocks=normal --print-cfg --print-reordered \
RUN:   --funcs=main,SolveCubic,usqrt --sequential-disassembly \
RUN:   2>&1 | FileCheck %s

# Original order
CHECK:     Binary Function "main"
CHECK:     BB Layout : .LBB00, .Ltmp1, .Ltmp0, .Ltmp3, .Ltmp2, .Ltmp5, .Ltmp4, .Ltmp7, .Ltmp6, .Ltmp9, .Ltmp8, .Ltmp11, .Ltmp10, .Ltmp13, .Ltmp12, .Ltmp15, .Ltmp14, .Ltmp21, .Ltmp20, .Ltmp19, .Ltmp18, .Ltmp17, .Ltmp16, .LFT0, .LFT1, .LFT2, .LFT3, .Ltmp22, .LFT4, .Ltmp23, .LFT5, .Ltmp24, .LFT6, .Ltmp25, .LFT7
CHECK:     Binary Function "SolveCubic"
CHECK:     BB Layout : .LBB01, .LFT8, .LFT9, .Ltmp33, .LFT10, .Ltmp28, .LFT11, .Ltmp26, .LFT12, .Ltmp37, .Ltmp36, .Ltmp35, .Ltmp34, .Ltmp27, .Ltmp32, .Ltmp31, .Ltmp30, .Ltmp29
CHECK:     Binary Function "usqrt"
CHECK:     BB Layout : .LBB02, .Ltmp39, .LFT13, .Ltmp38, .LFT14

# New order
CHECK:     Binary Function "main"
CHECK:     BB Layout : .LBB00, .Ltmp1, .Ltmp0, .Ltmp3, .Ltmp2, .Ltmp5, .Ltmp4, .Ltmp7, .Ltmp6, .Ltmp9, .Ltmp8, .Ltmp11, .Ltmp10, .Ltmp13, .Ltmp12, .Ltmp15, .Ltmp14, .Ltmp21, .Ltmp16, .Ltmp18, .Ltmp17, .LFT0, .Ltmp19, .LFT1, .Ltmp20, .LFT2, .LFT3, .Ltmp22, .LFT4, .Ltmp23, .LFT5, .Ltmp24, .LFT6, .Ltmp25, .LFT7
CHECK:     Binary Function "SolveCubic"
CHECK:     BB Layout : .LBB01, .Ltmp26, .LFT12, .Ltmp37, .Ltmp36, .Ltmp35, .Ltmp34, .LFT8, .LFT9, .Ltmp33, .Ltmp28, .LFT10, .Ltmp27, .Ltmp32, .Ltmp31, .Ltmp30, .Ltmp29
CHECK:     Binary Function "usqrt"
CHECK:     BB Layout : .LBB02, .Ltmp38, .Ltmp39, .LFT13, .LFT14
