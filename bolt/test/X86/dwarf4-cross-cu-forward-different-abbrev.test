# REQUIRES: system-linux

# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-cross-reference-different-abbrev-dst.s -o %t.o
# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-cross-reference-different-abbrev-src.s -o %t1.o
# RUN: %clang %cflags -gdwarf-4 %t1.o %t.o -o %t.exe
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections --debug-thread-count=4 --cu-processing-batch-size=4
# RUN: llvm-dwarfdump --show-form --verbose --debug-info %t.exe | FileCheck --check-prefix=PRECHECK %s
# RUN: llvm-dwarfdump --show-form --verbose --debug-info %t.bolt | FileCheck --check-prefix=POSTCHECK %s

## This test checks that BOL<PERSON> handles forward cross CU references for dwarf4
## when CUs are have different abbrev tables.

# PRECHECK: DW_TAG_compile_unit
# PRECHECK: DW_AT_abstract_origin [DW_FORM_ref_addr]
# PRECHECK-SAME: "inlined"
# PRECHECK: DW_AT_abstract_origin [DW_FORM_ref_addr]
# PRECHECK-SAME: "var"
# PRECHECK: DW_TAG_compile_unit

# POSTCHECK: DW_TAG_compile_unit
# POSTCHECK: DW_AT_abstract_origin [DW_FORM_ref_addr]
# POSTCHECK-SAME: "inlined"
# POSTCHECK: DW_AT_abstract_origin [DW_FORM_ref_addr]
# POSTCHECK-SAME: "var"
