; RUN: rm -rf %t
; RUN: mkdir %t
; RUN: cd %t
; RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-df-dualcu-loclist-main.s \
; RUN: -split-dwarf-file=main.dwo -o main.o
; RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-df-dualcu-loclist-helper.s \
; RUN: -split-dwarf-file=helper.dwo -o helper.o
; RUN: %clang %cflags -gdwarf-5 -O2 -gsplit-dwarf=split main.o helper.o -o main.exe
; RUN: llvm-bolt main.exe -o main.exe.bolt --update-debug-sections --debug-thread-count=4 --cu-processing-batch-size=4
; RUN: llvm-dwarfdump --show-form --verbose --debug-info main.dwo | FileCheck -check-prefix=PRE-BOLT-DWO-MAIN %s
; RUN: llvm-dwarfdump --show-form --verbose --debug-info main.dwo.dwo | FileCheck -check-prefix=BOLT-DWO-MAIN %s
; RUN: llvm-dwarfdump --show-form --verbose --debug-info helper.dwo | FileCheck -check-prefix=PRE-BOLT-DWO-HELPER %s
; RUN: llvm-dwarfdump --show-form --verbose --debug-info helper.dwo.dwo | FileCheck -check-prefix=BOLT-DWO-HELPER %s

;; Testing dwarf4 split dwarf for two CUs. Making sure DW_AT_location [DW_FORM_sec_offset] is updated correctly.

; PRE-BOLT-DWO-MAIN: version = 0x0004
; PRE-BOLT-DWO-MAIN: DW_TAG_formal_parameter [10]
; PRE-BOLT-DWO-MAIN-NEXT: DW_AT_location [DW_FORM_sec_offset] (0x00000000:
; PRE-BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000003, 0x0000000000000014)
; PRE-BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000005, 0x0000000000000005)
; PRE-BOLT-DWO-MAIN: DW_TAG_formal_parameter [10]
; PRE-BOLT-DWO-MAIN-NEXT: DW_AT_location [DW_FORM_sec_offset] (0x00000016:
; PRE-BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000003, 0x000000000000000c)
; PRE-BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000006, 0x000000000000000d)

; BOLT-DWO-MAIN: version = 0x0004
; BOLT-DWO-MAIN: DW_TAG_formal_parameter [10]
; BOLT-DWO-MAIN-NEXT: DW_AT_location [DW_FORM_sec_offset] (0x00000010:
; BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000003, 0x0000000000000014)
; BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000004, 0x0000000000000005)
; BOLT-DWO-MAIN: DW_TAG_formal_parameter [10]
; BOLT-DWO-MAIN-NEXT: DW_AT_location [DW_FORM_sec_offset] (0x00000026:
; BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000003, 0x000000000000000c)
; BOLT-DWO-MAIN-NEXT: DW_LLE_startx_length   (0x0000000000000005, 0x000000000000000d)


; PRE-BOLT-DWO-HELPER: version = 0x0004
; PRE-BOLT-DWO-HELPER: DW_TAG_formal_parameter [5]
; PRE-BOLT-DWO-HELPER-NEXT: DW_AT_location [DW_FORM_sec_offset] (0x00000000:
; PRE-BOLT-DWO-HELPER-NEXT: DW_LLE_startx_length   (0x0000000000000002, 0x0000000000000008)
; PRE-BOLT-DWO-HELPER-NEXT: DW_LLE_startx_length   (0x0000000000000003, 0x0000000000000016)

; BOLT-DWO-HELPER: version = 0x0004
; BOLT-DWO-HELPER: DW_TAG_formal_parameter [5]
; BOLT-DWO-HELPER-NEXT: DW_AT_location [DW_FORM_sec_offset] (0x00000010:
; BOLT-DWO-HELPER-NEXT: DW_LLE_startx_length   (0x0000000000000002, 0x0000000000000008): DW_OP_reg5 RDI
; BOLT-DWO-HELPER-NEXT: DW_LLE_startx_length   (0x0000000000000003, 0x0000000000000016)
