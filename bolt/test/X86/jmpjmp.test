## Verifies that llvm-bolt allocates two consecutive jumps in two separate basic
## blocks.

RUN: llvm-mc -filetype=obj -triple x86_64-unknown-unknown %S/Inputs/jmpjmp.s -o %t.o
RUN: %clang %cflags %t.o -o %t.exe
RUN: llvm-bolt %t.exe -o %t.null --print-cfg 2>&1 | FileCheck %s

CHECK:  Binary Function "testfunc"
CHECK:    State       : CFG constructed
CHECK:    Section     : .text
CHECK:    IsSimple    : 1
CHECK:    BB Count    : 4
CHECK:  }
CHECK:  .LBB{{.*}}
CHECK:      {{.*}}:   jmp     .Ltmp{{.*}}
CHECK:    Successors: .Ltmp{{.*}}
CHECK:  .LFT{{.*}}
CHECK:      {{.*}}:   jmp     .Ltmp{{.*}}
CHECK:    Successors: .Ltmp{{.*}}
CHECK:  .Ltmp{{.*}}
CHECK:    Predecessors: .LBB{{.*}}
CHECK:      {{.*}}:   jmp     .Ltmp{{.*}}
CHECK:    Successors: .Ltmp{{.*}}
CHECK:  .Ltmp{{.*}}
CHECK:    Predecessors: .LFT{{.*}}, .Ltmp{{.*}}
CHECK:      {{.*}}:   retq
