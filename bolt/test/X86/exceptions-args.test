## Check that we handle GNU_args_size correctly.
## It is generated for throwing functions with LP that have parameters on stack.

RUN: %clang %cflags %p/../Inputs/stub.c -fPIC -pie -shared -o %t.so
RUN: %clangxx %cxxflags -no-pie %p/Inputs/exc_args.s -o %t %t.so -Wl,-z,notext
RUN: llvm-bolt %t -o %t.null --print-finalized --print-only=main | FileCheck %s

CHECK: Binary Function "main" after finalize-functions
CHECK:      callq	_Z3fooiiiiiiii {{.*}} GNU_args_size = 16
CHECK:      callq	printf@PLT {{.*}} GNU_args_size = 0
CHECK: End of Function "main"
