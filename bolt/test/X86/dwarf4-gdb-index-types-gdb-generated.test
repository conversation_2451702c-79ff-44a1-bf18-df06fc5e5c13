# REQUIRES: system-linux

# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-gdb-index-types-main.s -o %tmaingdb.o
# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-gdb-index-types-helper.s -o %thelpergdb.o
# RUN: %clang %cflags %tmaingdb.o %thelpergdb.o -o %tgdb.exe -Wl,-q
# RUN: llvm-objcopy %tgdb.exe --add-section=.gdb_index=%p/Inputs/dwarf4-gdb-index-types-v8.gdb-index
# RUN: llvm-bolt %tgdb.exe -o %tgdb.bolt --update-debug-sections
# RUN: llvm-dwarfdump --gdb-index %tgdb.bolt | FileCheck --check-prefix=POSTCHECK %s

## Tests that BOLT correctly handles gdb-index generated by GDB.

# POSTCHECK:          Version = 8
# POSTCHECK:          CU list offset = 0x18, has 2 entries
# POSTCHECK-NEXT:         0: Offset = 0x0, Length = 0x6e
# POSTCHECK-NEXT:         1: Offset = 0x6e, Length = 0x72
# POSTCHECK:          Types CU list offset = 0x38, has 2 entries
# POSTCHECK-NEXT:         0: offset = 0x00000000, type_offset = 0x0000001e, type_signature = 0x418503b8111e9a7b
# POSTCHECK-NEXT:         1: offset = 0x00000044, type_offset = 0x0000001e, type_signature = 0x00f6cca4e3a15118
# POSTCHECK:          Address area offset = 0x68, has 2 entries
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR:]],
# POSTCHECK-SAME:           0x[[#ADDR + 0xf]]) (Size: 0xf), CU id = 0
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR1:]],
# POSTCHECK-SAME:           0x[[#ADDR1 + 0xd]]) (Size: 0xd), CU id = 1
# POSTCHECK:          Symbol table offset = 0x90, size = 1024, filled slots
# POSTCHECK-NEXT:         2: Name offset = 0x28, CU vector offset = 0x0
# POSTCHECK-NEXT:               String name: S, CU vector index: 0
# POSTCHECK-NEXT:         71: Name offset = 0x2a, CU vector offset = 0x8
# POSTCHECK-NEXT:           String name: S2, CU vector index: 1
# POSTCHECK-NEXT:         489: Name offset = 0x2d, CU vector offset = 0x10
# POSTCHECK-NEXT:           String name: main, CU vector index: 2
# POSTCHECK-NEXT:         661: Name offset = 0x32, CU vector offset = 0x18
# POSTCHECK-NEXT:           String name: foo, CU vector index: 3
# POSTCHECK-NEXT:         732: Name offset = 0x36, CU vector offset = 0x20
# POSTCHECK-NEXT:           String name: unsigned int, CU vector index: 4
# POSTCHECK-NEXT:         754: Name offset = 0x43, CU vector offset = 0x0
# POSTCHECK-NEXT:           String name: int, CU vector index: 0
# POSTCHECK:          Constant pool offset = 0x2090, has 5 CU vectors
# POSTCHECK-NEXT:         0(0x0): 0x90000000
# POSTCHECK-NEXT:         1(0x8): 0x90000001
# POSTCHECK-NEXT:         2(0x10): 0x30000000
# POSTCHECK-NEXT:         3(0x18): 0x30000001
# POSTCHECK-NEXT:         4(0x20): 0x90000002
