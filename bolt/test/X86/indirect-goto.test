## Check llvm-bolt processes binaries compiled from sources that use indirect goto.
RUN: %clang %cflags -no-pie %S/../Inputs/indirect_goto.c -Wl,-q -o %t
RUN: llvm-bolt %t -o %t.null --relocs=1 --print-cfg --print-only=main \
RUN:   --strict \
RUN:   2>&1 | FileCheck %s

## Check that all possible destinations are included as successors.
CHECK:  jmpq    *%rax # UNKNOWN CONTROL FLOW
CHECK:  Successors: .Ltmp0, .Ltmp1, .Ltmp2
