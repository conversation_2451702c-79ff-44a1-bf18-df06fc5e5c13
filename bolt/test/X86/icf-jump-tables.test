## Check that BOLT folds functions with jump tables and does not report
## undefined symbols.

# REQUIRES: system-linux

# RUN: %clang %cflags -O1 -g %p/../Inputs/icf-jump-tables.c -o %t.exe -Wl,-q
# RUN: llvm-bolt %t.exe --icf -o %t.bolt 2>&1 | FileCheck %s

## Check that BOLT successfully folded a function with jump table:
# CHECK: ICF folded {{.*}}. {{[^0]}} functions had jump tables.

## Check that no relocations against undefined symbols were emitted:
# CHECK-NOT: Undefined temporary symbol
