## Checks that llvm-bolt is able to read data generated by perf2bolt and update
## the CFG edges accordingly with absolute number of branches and mispredictions.
## Also checks that llvm-bolt disassembler and CFG builder is working properly.

RUN: yaml2obj %p/Inputs/blarge.yaml &> %t.exe
RUN: llvm-bolt %t.exe -o %t.null --data %p/Inputs/blarge.fdata --print-cfg | FileCheck %s

CHECK:    Binary Function "usqrt"
CHECK:      State       : CFG constructed
CHECK:      Address     : 0x401170
CHECK:      Size        : 0x43
CHECK:      MaxSize     : 0x43
CHECK:      Offset      : 0xcb0
CHECK:      Section     : .text
CHECK:      IsSimple    : 1
CHECK:      BB Count    : 5
CHECK:      Exec Count  : 199
CHECK:      Sample Count: 7689
CHECK:    }
CHECK:    .LBB{{.*}}
CHECK:      Exec Count : 199
CHECK:        {{.*}}:   movl    $0x20, %r8d
CHECK:        {{.*}}:   xorl    %eax, %eax
CHECK:        {{.*}}:   xorl    %edx, %edx
CHECK:      Successors: .Ltmp{{.*}} (mispreds: 0, count: 0)
CHECK:    .Ltmp{{.*}}
CHECK:      Exec Count : 4711
CHECK:      Predecessors: .Ltmp{{.*}}, .LBB{{.*}}
CHECK:        {{.*}}:   movq    %rdi, %rcx
CHECK:        {{.*}}:   addq    %rax, %rax
CHECK:        {{.*}}:   shlq    $0x2, %rdi
CHECK:        {{.*}}:   andl    $0xc0000000, %ecx
CHECK:        {{.*}}:   shrq    $0x1e, %rcx
CHECK:        {{.*}}:   leaq    (%rcx,%rdx,4), %rdx
CHECK:        {{.*}}:   leaq    0x1(%rax,%rax), %rcx
CHECK:        {{.*}}:   cmpq    %rcx, %rdx
CHECK:        {{.*}}:   jb      .Ltmp{{.*}}
CHECK:      Successors: .Ltmp{{.*}} (mispreds: 171, count: 2886), .LFT{{.*}} (mispreds: 0, count: 0)
CHECK:    .LFT{{.*}}
CHECK:      Exec Count : 0
CHECK:      Predecessors: .Ltmp{{.*}}
CHECK:        {{.*}}:   subq    %rcx, %rdx
CHECK:        {{.*}}:   addq    $0x1, %rax
CHECK:      Successors: .Ltmp{{.*}} (mispreds: 0, count: 0)
CHECK:    .Ltmp{{.*}}
CHECK:      Exec Count : 4711
CHECK:      Predecessors: .Ltmp{{.*}}, .LFT{{.*}}
CHECK:        {{.*}}:   subl    $0x1, %r8d
CHECK:        {{.*}}:   jne     .Ltmp{{.*}}
CHECK:      Successors: .Ltmp{{.*}} (mispreds: 33, count: 4711), .LFT{{.*}} (mispreds: 0, count: 0)
CHECK:    .LFT{{.*}}
CHECK:      Exec Count : 0
CHECK:      Predecessors: .Ltmp{{.*}}
CHECK:        {{.*}}:   movq    %rax, (%rsi)
CHECK:        {{.*}}:   retq
CHECK:    End of Function "usqrt"
