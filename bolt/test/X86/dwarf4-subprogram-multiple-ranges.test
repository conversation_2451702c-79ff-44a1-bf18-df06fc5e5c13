# REQUIRES: system-linux

# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-subprogram-multiple-ranges-main.s -o %t1.o
# RUN: %clang %cflags %t1.o -o %t.exe -Wl,-q
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections
# RUN: llvm-objdump %t.bolt --disassemble > %t1.txt
# RUN: llvm-dwarfdump --show-form --verbose --debug-info %t.bolt >> %t1.txt
# RUN: cat %t1.txt | FileCheck --check-prefix=POSTCHECK %s

## This test checks BOL<PERSON> correctly handles DW_TAG_subprogram with Ranges with multiple entries.

# POSTCHECK: _Z7doStuffi>:
# POSTCHECK: [[#%.6x,ADDR:]]
# POSTCHECK: _Z7doStuffi.__part.1>:
# POSTCHECK-NEXT: [[#%.6x,ADDR1:]]
# POSTCHECK: _Z7doStuffi.__part.2>:
# POSTCHECK-NEXT: [[#%.6x,ADDR2:]]

# POSTCHECK: DW_TAG_subprogram
# POSTCHECK-NEXT: DW_AT_ranges
# POSTCHECK-NEXT:   [0x0000000000[[#ADDR1]], 0x0000000000[[#ADDR1 + 0xb]])
# POSTCHECK-NEXT:   [0x0000000000[[#ADDR2]], 0x0000000000[[#ADDR2 + 0x5]])
# POSTCHECK-NEXT:   [0x0000000000[[#ADDR]], 0x0000000000[[#ADDR + 0xf]]))
