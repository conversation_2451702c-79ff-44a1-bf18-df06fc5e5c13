## Check new BAT format containing hashes for YAML profile.

RUN: yaml2obj %p/Inputs/blarge_new.yaml &> %t.exe
RUN: llvm-bolt %t.exe -o %t.out --pa -p %p/Inputs/blarge_new.preagg.txt \
RUN:   --reorder-blocks=ext-tsp --split-functions --split-strategy=cdsplit \
RUN:   --reorder-functions=cdsort --enable-bat --dyno-stats --skip-funcs=main \
RUN:   2>&1 | FileCheck --check-prefix WRITE-BAT-CHECK %s
## Check that branch with entry in BAT is accounted for.
RUN: perf2bolt %t.out --pa -p %p/Inputs/blarge_new_bat_branchentry.preagg.txt \
RUN:   -w %t.yaml -o %t.fdata
RUN: llvm-bolt %t.exe -data %t.fdata -w %t.yaml-fdata -o %t.null
RUN: FileCheck --input-file %t.yaml --check-prefix BRANCHENTRY-YAML-CHECK %s
RUN: FileCheck --input-file %t.yaml-fdata --check-prefix BRANCHENTRY-YAML-CHECK %s
BRANCHENTRY-YAML-CHECK:    - name: SolveCubic
BRANCHENTRY-YAML-CHECK:      bid: 0
BRANCHENTRY-YAML-CHECK:      hash: 0x700F19D24600000
BRANCHENTRY-YAML-CHECK-NEXT: succ: [ { bid: 7, cnt: 1 }
## Check that the order is correct between BAT YAML and FDATA->YAML.
RUN: perf2bolt %t.out --pa -p %p/Inputs/blarge_new_bat_order.preagg.txt \
RUN:   -w %t.yaml -o %t.fdata
RUN: llvm-bolt %t.exe -data %t.fdata -w %t.yaml-fdata -o %t.null
RUN: FileCheck --input-file %t.yaml --check-prefix ORDER-YAML-CHECK %s
RUN: FileCheck --input-file %t.yaml-fdata --check-prefix ORDER-YAML-CHECK %s
ORDER-YAML-CHECK:    - name: SolveCubic
ORDER-YAML-CHECK:      bid: 3
ORDER-YAML-CHECK:      hash: 0xDDA1DC5F69F900AC
ORDER-YAML-CHECK-NEXT: calls: [ { off: 0x26, fid: [[#]], cnt: 20 } ]
ORDER-YAML-CHECK-NEXT: succ: [ { bid: 5, cnt: 7 }
## Large profile test
RUN: perf2bolt %t.out --pa -p %p/Inputs/blarge_new_bat.preagg.txt -w %t.yaml -o %t.fdata \
RUN:   --heatmap %t.hm 2>&1 | FileCheck --check-prefix READ-BAT-CHECK %s
RUN: FileCheck --input-file %t.yaml --check-prefix YAML-BAT-CHECK %s
RUN: FileCheck --input-file %t.hm-section-hotness.csv --check-prefix CHECK-HM %s
## Check that YAML converted from fdata matches YAML created directly with BAT.
RUN: llvm-bolt %t.exe -data %t.fdata -w %t.yaml-fdata -o /dev/null \
RUN:   2>&1 | FileCheck --check-prefix READ-BAT-FDATA-CHECK %s
RUN: FileCheck --input-file %t.yaml-fdata --check-prefix YAML-BAT-CHECK %s

## Test resulting YAML profile with the original binary (no-stale mode)
RUN: llvm-bolt %t.exe -data %t.yaml -o %t.null -dyno-stats 2>&1 \
RUN:   | FileCheck --check-prefix CHECK-BOLT-YAML %s

WRITE-BAT-CHECK: BOLT-INFO: Wrote 5 BAT maps
WRITE-BAT-CHECK: BOLT-INFO: Wrote 4 function and 22 basic block hashes
WRITE-BAT-CHECK: BOLT-INFO: BAT section size (bytes): 404

READ-BAT-CHECK-NOT: BOLT-ERROR: unable to save profile in YAML format for input file processed by BOLT
READ-BAT-CHECK: BOLT-INFO: Parsed 5 BAT entries
READ-BAT-CHECK: PERF2BOLT: read 79 aggregated LBR entries
READ-BAT-CHECK: HEATMAP: building heat map
READ-BAT-CHECK: BOLT-INFO: 5 out of 21 functions in the binary (23.8%) have non-empty execution profile
READ-BAT-FDATA-CHECK: BOLT-INFO: 5 out of 16 functions in the binary (31.2%) have non-empty execution profile
CHECK-HM: .text, 0x800000, 0x8002cc, 38.7595, 91.6667, 0.3553

YAML-BAT-CHECK:      functions:
# Function not covered by BAT - has insns in basic block
YAML-BAT-CHECK:      - name:    main
YAML-BAT-CHECK-NEXT:   fid:     2
YAML-BAT-CHECK-NEXT:   hash:    0x9895746D48B2C876
YAML-BAT-CHECK-NEXT:   exec:    0
YAML-BAT-CHECK-NEXT:   nblocks: 46
YAML-BAT-CHECK-NEXT:   blocks:
YAML-BAT-CHECK-NEXT:   - bid:   0
YAML-BAT-CHECK-NEXT:     insns: 26
YAML-BAT-CHECK-NEXT:     hash:  0xA900AE79CFD40000
YAML-BAT-CHECK-NEXT:     succ:  [ { bid: 3, cnt: 0 }, { bid: 1, cnt: 0 } ]
# Check fallthroughs in non-BAT function
YAML-BAT-CHECK-NEXT:   - bid:   27
YAML-BAT-CHECK-NEXT:     insns: 3
YAML-BAT-CHECK-NEXT:     hash:  0x30A1EBA77A903F0
YAML-BAT-CHECK-NEXT:     succ:  [ { bid: 28, cnt: 1 } ]
# Calls from no-BAT to BAT function
YAML-BAT-CHECK:        - bid:   28
YAML-BAT-CHECK-NEXT:     insns: 13
YAML-BAT-CHECK-NEXT:     hash:  0xB2F04C1F25F00400
YAML-BAT-CHECK-NEXT:     calls: [ { off: 0x21, fid: [[#SOLVECUBIC:]], cnt: 25 }, { off: 0x2D, fid: [[#]], cnt: 9 } ]
# Function covered by BAT with calls
YAML-BAT-CHECK:      - name:    SolveCubic
YAML-BAT-CHECK-NEXT:   fid:     [[#SOLVECUBIC]]
YAML-BAT-CHECK-NEXT:   hash:    0x6AF7E61EA3966722
YAML-BAT-CHECK-NEXT:   exec:    25
YAML-BAT-CHECK-NEXT:   nblocks: 15
YAML-BAT-CHECK-NEXT:   blocks:
YAML-BAT-CHECK-NEXT:   - bid:   0
YAML-BAT-CHECK-NEXT:     insns: [[#]]
YAML-BAT-CHECK-NEXT:     hash:  0x700F19D24600000
YAML-BAT-CHECK-NEXT:     exec:  25
YAML-BAT-CHECK:        - bid:   3
YAML-BAT-CHECK-NEXT:     insns: [[#]]
YAML-BAT-CHECK-NEXT:     hash:  0xDDA1DC5F69F900AC
YAML-BAT-CHECK-NEXT:     calls: [ { off: 0x26, fid: [[#]], cnt: [[#]] } ]
YAML-BAT-CHECK-NEXT:     succ:  [ { bid: 5, cnt: [[#]] }
# Function covered by BAT - doesn't have insns in basic block
YAML-BAT-CHECK:      - name:    usqrt
YAML-BAT-CHECK-NEXT:   fid:     [[#]]
YAML-BAT-CHECK-NEXT:   hash:    0x99E67ED32A203023
YAML-BAT-CHECK-NEXT:   exec:    21
YAML-BAT-CHECK-NEXT:   nblocks: 5
YAML-BAT-CHECK-NEXT:   blocks:
YAML-BAT-CHECK:        - bid:   1
YAML-BAT-CHECK-NEXT:       insns: [[#]]
YAML-BAT-CHECK-NEXT:       hash:  0xD70DC695320E0010
YAML-BAT-CHECK-NEXT:       succ:  {{.*}} { bid: 2, cnt: [[#]]

CHECK-BOLT-YAML:      pre-processing profile using YAML profile reader
CHECK-BOLT-YAML-NEXT: 5 out of 16 functions in the binary (31.2%) have non-empty execution profile
CHECK-BOLT-YAML-NOT: invalid (possibly stale) profile
