# REQUIRES: system-linux

# RUN: rm -rf %t
# RUN: mkdir -p %t
# RUN: cd %t
# RUN: llvm-mc --split-dwarf-file=main.dwo   -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-split-gdb-index-types-main.s -o maingdb.o
# RUN: llvm-mc --split-dwarf-file=helper.dwo -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-split-gdb-index-types-helper.s -o helpergdb.o
# RUN: %clang %cflags maingdb.o helpergdb.o -o maingdb.exe -Wl,-q
# RUN: llvm-objcopy maingdb.exe --add-section=.gdb_index=%p/Inputs/dwarf4-split-gdb-index-types-v8.gdb-index
# RUN: llvm-bolt maingdb.exe -o maingdb.exe.bolt --update-debug-sections
# RUN: llvm-dwarfdump --gdb-index maingdb.exe.bolt | FileCheck --check-prefix=POSTCHECK %s

## Tests that BOLT correctly handles gdb-index generated by GDB with split-dwarf DWARF4.

# POSTCHECK:          Version = 8
# POSTCHECK:          CU list offset = 0x18, has 2 entries
# POSTCHECK-NEXT:         0: Offset = 0x0, Length = 0x34
# POSTCHECK-NEXT:         1: Offset = 0x34, Length = 0x34
# POSTCHECK:          Types CU list offset = 0x38, has 4 entries
# POSTCHECK-NEXT:       0: offset = 0x0000004a, type_offset = 0x0000001e, type_signature = 0xb4580bc1535df1e4
# POSTCHECK-NEXT:       1: offset = 0x00000000, type_offset = 0x0000001e, type_signature = 0x675d23e4f33235f2
# POSTCHECK-NEXT:       2: offset = 0x0000004a, type_offset = 0x0000001e, type_signature = 0x49dc260088be7e56
# POSTCHECK-NEXT:       3: offset = 0x00000000, type_offset = 0x0000001e, type_signature = 0x104ec427d2ebea6f
# POSTCHECK:          Address area offset = 0x98, has 2 entries
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR:]],
# POSTCHECK-SAME:           0x[[#ADDR + 0x7a]]) (Size: 0x7a), CU id = 0
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR1:]],
# POSTCHECK-SAME:           0x[[#ADDR1 + 0x8]]) (Size: 0x8), CU id = 1
# POSTCHECK:          Symbol table offset = 0xc0, size = 1024, filled slots
# POSTCHECK-NEXT:       192: Name offset = 0x38, CU vector offset = 0x0
# POSTCHECK-NEXT:         String name: Foo2, CU vector index: 0
# POSTCHECK-NEXT:       193: Name offset = 0x3d, CU vector offset = 0x8
# POSTCHECK-NEXT:         String name: Foo3, CU vector index: 1
# POSTCHECK-NEXT:       489: Name offset = 0x42, CU vector offset = 0x10
# POSTCHECK-NEXT:         String name: main, CU vector index: 2
# POSTCHECK-NEXT:       518: Name offset = 0x47, CU vector offset = 0x18
# POSTCHECK-NEXT:         String name: char, CU vector index: 3
# POSTCHECK-NEXT:       560: Name offset = 0x4c, CU vector offset = 0x20
# POSTCHECK-NEXT:         String name: Foo2a, CU vector index: 4
# POSTCHECK-NEXT:       634: Name offset = 0x52, CU vector offset = 0x28
# POSTCHECK-NEXT:         String name: Foo, CU vector index: 5
# POSTCHECK-NEXT:       661: Name offset = 0x56, CU vector offset = 0x30
# POSTCHECK-NEXT:         String name: foo, CU vector index: 6
# POSTCHECK-NEXT:       754: Name offset = 0x5a, CU vector offset = 0x18
# POSTCHECK-NEXT:         String name: int, CU vector index: 3
# POSTCHECK:          Constant pool offset = 0x20c0, has 7 CU vectors
# POSTCHECK-NEXT:       0(0x0): 0x10000003
# POSTCHECK-NEXT:       1(0x8): 0x10000005
# POSTCHECK-NEXT:       2(0x10): 0x30000000
# POSTCHECK-NEXT:       3(0x18): 0x90000000
# POSTCHECK-NEXT:       4(0x20): 0x10000004
