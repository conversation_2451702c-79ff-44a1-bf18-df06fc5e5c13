## Test the double jump removal peephole.

## This test has commands that rely on shell capabilities that won't execute
## correctly on Windows e.g. subshell execution
REQUIRES: shell

RUN: %clangxx %cxxflags %p/Inputs/double_jump.cpp -o %t.exe
RUN: (llvm-bolt %t.exe --peepholes=double-jumps \
RUN:   --eliminate-unreachable -o %t 2>&1 \
RUN:   && llvm-objdump -d %t --print-imm-hex --no-show-raw-insn) | FileCheck %s

CHECK: BOLT-INFO: Peephole: 1 double jumps patched.

CHECK: <_Z3foom>:
CHECK-NEXT: pushq %rbp
CHECK-NEXT: movq  %rsp, %rbp
CHECK-NEXT: movq  %rdi, -0x8(%rbp)
CHECK-NEXT: cmpq  $0x1, -0x8(%rbp)
CHECK-NEXT: je    {{.*}} <_Z3foom+0x2c>
CHECK-NEXT: incq  -0x8(%rbp)
CHECK-NEXT: incq  -0x8(%rbp)
CHECK-NEXT: cmpq  $0x2, -0x8(%rbp)
CHECK-NEXT: je    {{.*}} <_Z3foom+0x22>
CHECK-NEXT: incq  -0x8(%rbp)
CHECK-NEXT: movq  -0x8(%rbp), %rdi
CHECK-NEXT: popq  %rbp
CHECK-NEXT: jmp   {{.*}} <bar>
CHECK-NEXT: popq  %rbp
CHECK-NEXT: incq  -0x8(%rbp)
CHECK-NEXT: jmp   {{.*}} <bar>
