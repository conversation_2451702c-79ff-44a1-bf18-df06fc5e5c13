# REQUIRES: system-linux

# RUN: rm -rf %t
# RUN: mkdir -p %t
# RUN: cd %t
# RUN: llvm-mc --split-dwarf-file=main.dwo   -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5-split-gdb-index-types-main.s -o maingdb.o
# RUN: llvm-mc --split-dwarf-file=helper.dwo -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5-split-gdb-index-types-helper.s -o helpergdb.o
# RUN: %clang %cflags maingdb.o helpergdb.o -o maingdb.exe -Wl,-q
# RUN: llvm-objcopy maingdb.exe --add-section=.gdb_index=%p/Inputs/dwarf5-split-gdb-index-types-v8.gdb-index
# RUN: llvm-bolt maingdb.exe -o maingdb.exe.bolt --update-debug-sections --debug-thread-count=4 --cu-processing-batch-size=4
# RUN: llvm-dwarfdump --gdb-index maingdb.exe.bolt | FileCheck --check-prefix=POSTCHECK %s

## Tests that BOLT correctly handles gdb-index generated by GDB with split-dwarf DWARF4.

# POSTCHECK:          Version = 8
# POSTCHECK:          CU list offset = 0x18, has 2 entries
# POSTCHECK-NEXT:         0: Offset = 0x0, Length = 0x29
# POSTCHECK-NEXT:         1: Offset = 0x29, Length = 0x29
# POSTCHECK:          Types CU list offset = 0x38, has 0 entries
# POSTCHECK:          Address area offset = 0x38, has 2 entries
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR:]],
# POSTCHECK-SAME:           0x[[#ADDR + 0x7a]]) (Size: 0x7a), CU id = 0
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR1:]],
# POSTCHECK-SAME:           0x[[#ADDR1 + 0x8]]) (Size: 0x8), CU id = 1
# POSTCHECK:          Symbol table offset = 0x60, size = 1024, filled slots
# POSTCHECK-NEXT:       489: Name offset = 0x18, CU vector offset = 0x0
# POSTCHECK-NEXT:         String name: main, CU vector index: 0
# POSTCHECK-NEXT:       518: Name offset = 0x1d, CU vector offset = 0x8
# POSTCHECK-NEXT:         String name: char, CU vector index: 1
# POSTCHECK-NEXT:       661: Name offset = 0x22, CU vector offset = 0x10
# POSTCHECK-NEXT:         String name: foo, CU vector index: 2
# POSTCHECK-NEXT:       754: Name offset = 0x26, CU vector offset = 0x8
# POSTCHECK-NEXT:         String name: int, CU vector index: 1
# POSTCHECK:          Constant pool offset = 0x2060, has 3 CU vectors
# POSTCHECK-NEXT:       0(0x0): 0x30000000
# POSTCHECK-NEXT:       1(0x8): 0x90000000
# POSTCHECK-NEXT:       2(0x10): 0x30000001
