## Tests function matching in YAMLProfileReader by function hash.

# REQUIRES: system-linux
# RUN: split-file %s %t
# RUN: llvm-mc -filetype=obj -triple x86_64-unknown-unknown %t/main.s -o %t.o
# RUN: %clang %cflags %t.o -o %t.exe -Wl,-q -nostdlib
# RUN: llvm-bolt %t.exe -o %t.out --data %t/yaml -v=2 \
# RUN:   --print-cfg --match-profile-with-function-hash --profile-ignore-hash=0 2>&1 | FileCheck %s

# CHECK: BOLT-INFO: matched 1 functions with hash

#--- main.s
.globl main
.type	main, @function
main:
  .cfi_startproc
.LBB00:
  pushq   %rbp
  movq    %rsp, %rbp
  subq    $16, %rsp
  testq   %rax, %rax
  js      .LBB03
.LBB01:
  jne     .LBB04
.LBB02:
  nop
.LBB03:
  xorl    %eax, %eax
  addq    $16, %rsp
  popq    %rbp
  retq
.LBB04:
  xorl    %eax, %eax
  addq    $16, %rsp
  popq    %rbp
  retq
## For relocations against .text
  .reloc 0, R_X86_64_NONE
  .cfi_endproc
  .size	main, .-main

#--- yaml
---
header:
  profile-version: 1
  binary-name:     'hashing-based-function-matching.s.tmp.exe'
  binary-build-id: '<unknown>'
  profile-flags:   [ lbr ]
  profile-origin:  branch profile reader
  profile-events:  ''
  dfs-order:       false
  hash-func:       xxh3
functions:
  - name:            main2
    fid:             0
    hash:            0x6E7F15254DE2478
    exec:            1
    nblocks:         6
    blocks:
      - bid:             1
        insns:           1
        succ:            [ { bid: 3, cnt: 1} ]
...
