# RUN: yaml2obj %p/Inputs/plt-sec.yaml &> %t.exe
# RUN: llvm-bolt %t.exe -o %t.out

# RUN: llvm-readelf --program-headers %t.out | grep LOAD | tail -n 1 > %t.load
# RUN: llvm-nm %t.out >> %t.load
# RUN: FileCheck %s < %t.load

## Check that llvm-bolt correctly updates _end symbol to match the end of the
## last loadable segment.
CHECK: LOAD 0x[[#%x,OFFSET:]] 0x[[#%x,VMA:]] 0x[[#%x,PA:]] 0x[[#%x,FILESIZE:]] 0x[[#%x,MEMSIZE:]]
CHECK: [[#%.16x, VMA + MEMSIZE]] {{.}} _end
