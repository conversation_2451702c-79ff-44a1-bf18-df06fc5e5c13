## This reproduces issue 20 from our github repo
##  "BOL<PERSON> crashes when removing unreachable BBs that are a target
##   in a JT"

# RUN: yaml2obj %p/Inputs/issue20.yaml &> %t.exe
# RUN: llvm-bolt %t.exe --relocs=0 --jump-tables=move --print-finalized \
# RUN:    -o %t.out | FileCheck %s

CHECK-NOT: BOLT-INFO: UCE removed {{.*}} blocks and {{.*}}| bytes of code
CHECK: Binary Function "main"
CHECK:      .LFT0 (2 instructions, align : 1)
CHECK-NEXT:     00000004:   andq
CHECK-NEXT:     00000008:   jmpq
CHECK-NEXT:   Successors: .Ltmp1, .Ltmp2, .Ltmp3, .Ltmp4
