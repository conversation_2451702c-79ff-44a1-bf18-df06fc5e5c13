; RUN: rm -rf %t
; RUN: mkdir %t
; RUN: cd %t
; RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-df-change-in-dw-op-gnu-addr-index-main.s \
; RUN: -split-dwarf-file=main.dwo -o main.o
; RUN: %clang %cflags -gdwarf-5 -gsplit-dwarf=split main.o -o main.exe
; RUN: llvm-bolt main.exe -o main.exe.bolt --update-debug-sections
; RUN: not llvm-dwarfdump --show-form --verbose --debug-info main.dwo &> %t/maindwo.txt
; RUN: cat %t/maindwo.txt | FileCheck -check-prefix=PRE-BOLT-DWO-MAIN %s
; RUN: not llvm-dwarfdump --show-form --verbose --debug-info main.dwo.dwo &> %t/maindwodwo.txt
; RUN: cat %t/maindwodwo.txt | FileCheck -check-prefix=BOLT-DWO-MAIN %s

;; Tests that new indices are assigned to DW_OP_GNU_addr_index.

; PRE-BOLT-DWO-MAIN: version = 0x0004
; PRE-BOLT-DWO-MAIN: DW_AT_location [DW_FORM_exprloc]  (DW_OP_GNU_addr_index 0x0)
; PRE-BOLT-DWO-MAIN: DW_AT_location [DW_FORM_exprloc]  (DW_OP_GNU_addr_index 0x1)
; PRE-BOLT-DWO-MAIN: DW_AT_location [DW_FORM_exprloc]  (DW_OP_GNU_addr_index 0x2)

; BOLT-DWO-MAIN: version = 0x0004
; BOLT-DWO-MAIN: DW_AT_location [DW_FORM_exprloc]  (DW_OP_GNU_addr_index 0x1)
; BOLT-DWO-MAIN: DW_AT_location [DW_FORM_exprloc]  (DW_OP_GNU_addr_index 0x6)
; BOLT-DWO-MAIN: DW_AT_location [DW_FORM_exprloc]  (DW_OP_GNU_addr_index 0x7)
