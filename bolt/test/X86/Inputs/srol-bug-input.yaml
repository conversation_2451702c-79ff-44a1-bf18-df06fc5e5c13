--- !ELF
FileHeader:
  Class:           ELFCLASS64
  Data:            ELFDATA2LSB
  Type:            ET_EXEC
  Machine:         EM_X86_64
  Entry:           0x0000000000400000
Sections:
  - Name:            .text
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x0000000000400000
    AddressAlign:    0x0000000000000010
    Content:         0315fa0000002B15f40000002315ee0000000B15e80000003315e2000000500fB715da0000008A15d4000000668B15cd0000008B15c7000000488B15c00000003A15ba000000663B15b30000003B15ad000000483B15a60000008415a0000000668515990000008515930000004885158c000000C3
  - Name:            .rodata
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x0000000000400100
    AddressAlign:    0x0000000000000100
    Content:         010002000000000000000000
  - Name:            .dynamic
    Type:            SHT_DYNAMIC
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x000000000061ADA8
    Link:            .dynstr
    AddressAlign:    0x0000000000000008
    Content:         01000000000000000100000000000000010000000000000096000000000000000100000000000000C400000000000000010000000000000001010000000000000C0000000000000028224000000000000D000000000000005C29410000000000190000000000000028A36100000000001B0000000000000008000000000000001A0000000000000030A36100000000001C000000000000000800000000000000F5FEFF6F0000000098024000000000000500000000000000300F4000000000000600000000000000D0024000000000000A00000000000000BC050000000000000B00000000000000180000000000000015000000000000000000000000000000030000000000000000B0610000000000020000000000000000000000000000001400000000000000070000000000000017000000000000006017400000000000070000000000000088164000000000000800000000000000000000000000000009000000000000001800000000000000FEFFFF6F00000000F815400000000000FFFFFF6F000000000200000000000000F0FFFF6F00000000EC14400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Symbols:
    - Name:            mydata
      Section:         .rodata
      Value:           0x0000000000400100
      Binding:         STB_GLOBAL
    - Name:            myfunc
      Type:            STT_FUNC
      Section:         .text
      Value:           0x0000000000400000
      Binding:         STB_GLOBAL
DynamicSymbols:
    - Name:            mydata
      Section:         .rodata
      Value:           0x0000000000400100
      Binding:         STB_GLOBAL
ProgramHeaders:
  - Type: PT_PHDR
    Flags: [ PF_X, PF_R ]
    VAddr: 0x00400000
    PAddr: 0x00400000
    FirstSec: .text
    LastSec: .text
  - Type: PT_LOAD
    Flags: [ PF_X, PF_R ]
    VAddr: 0x00400000
    PAddr: 0x00400000
    FirstSec: .text
    LastSec: .text
  - Type: PT_DYNAMIC
    Flags: [ PF_X, PF_R ]
    VAddr: 0x0061ADA8
    PAddr: 0x0064ADA8
    FirstSec: .dynamic
    LastSec: .dynamic
...
