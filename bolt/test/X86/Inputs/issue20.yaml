--- !ELF
FileHeader:
  Class:           ELFCLASS64
  Data:            ELFDATA2LSB
  Type:            ET_EXEC
  Machine:         EM_X86_64
  Entry:           0x00000000004004CD
Sections:
  - Name:            .text
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x00000000004003E0
    AddressAlign:    0x0000000000000010
    Content:         31ED4989D15E4889E24883E4F0505449C7C07005400048C7C10005400048C7C7CD044000E8B7FFFFFFF4660F1F440000B82F10600055482D281060004883F80E4889E577025DC3B8000000004885C074F45DBF28106000FFE00F1F8000000000B82810600055482D2810600048C1F8034889E54889C248C1EA3F4801D048D1F875025DC3BA000000004885D274F45D4889C6BF28106000FFE20F1F8000000000803D9D0B2000007511554889E5E87EFFFFFF5DC6058A0B200001F3C30F1F400048833D7809200000741EB8000000004885C0741455BF200E60004889E5FFD05DE97BFFFFFF0F1F00E973FFFFFF4831C0C34883E703FF24FD90054000B801000000EB13B802000000EB0CB803000000EB05B804000000C3660F1F84000000000041574189FF41564989F641554989D541544C8D25F808200055488D2DF8082000534C29E531DB48C1FD034883EC08E85DFEFFFF4885ED741E0F1F8400000000004C89EA4C89F64489FF41FF14DC4883C3014839EB75EA4883C4085B5D415C415D415E415FC390662E0F1F840000000000F3C3
  - Name:            .rodata
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x0000000000400580
    AddressAlign:    0x0000000000000008
    Content:         01000200000000000000000000000000DC04400000000000E304400000000000EA04400000000000F104400000000000
  - Name:            .dynamic
    Type:            SHT_DYNAMIC
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x0000000000600E28
    Link:            .dynstr
    AddressAlign:    0x0000000000000008
    Content:         010000000000000001000000000000000C0000000000000090034000000000000D0000000000000074054000000000001900000000000000100E6000000000001B0000000000000008000000000000001A00000000000000180E6000000000001C000000000000000800000000000000F5FEFF6F000000009802400000000000050000000000000000034000000000000600000000000000B8024000000000000A0000000000000038000000000000000B0000000000000018000000000000001500000000000000000000000000000003000000000000000010600000000000020000000000000000000000000000001400000000000000070000000000000017000000000000007803400000000000070000000000000060034000000000000800000000000000000000000000000009000000000000001800000000000000FEFFFF6F000000004003400000000000FFFFFF6F000000000100000000000000F0FFFF6F000000003803400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Symbols:
    - Name:            main
      Type:            STT_FUNC
      Section:         .text
      Value:           0x00000000004004CD
      Size:            0x000000000000002A
      Binding:         STB_GLOBAL
    - Name:            jumptbl
      Section:         .rodata
      Value:           0x0000000000400590
      Binding:         STB_GLOBAL
DynamicSymbols:
    - Name:            mydata
      Section:         .rodata
      Value:           0x0000000000400100
      Binding:         STB_GLOBAL
ProgramHeaders:
  - Type: PT_LOAD
    Flags: [ PF_X, PF_R ]
    VAddr: 0x004003E0
    PAddr: 0x004003E0
    FirstSec: .text
    LastSec: .text
  - Type: PT_DYNAMIC
    Flags: [ PF_X, PF_R ]
    VAddr: 0x00600E28
    PAddr: 0x00600E28
    FirstSec: .dynamic
    LastSec: .dynamic
...
