--- !ELF
FileHeader:
  Class:           ELFCLASS64
  Data:            ELFDATA2LSB
  Type:            ET_EXEC
  Machine:         EM_X86_64
  Entry:           0x400510
ProgramHeaders:
  - Type:            PT_PHDR
    Flags:           [ PF_R ]
    VAddr:           0x400040
    Align:           0x8
  - Type:            PT_INTERP
    Flags:           [ PF_R ]
    FirstSec:        .interp
    LastSec:         .interp
    VAddr:           0x400238
  - Type:            PT_LOAD
    Flags:           [ PF_X, PF_R ]
    FirstSec:        .interp
    LastSec:         .eh_frame
    VAddr:           0x400000
    Align:           0x200000
  - Type:            PT_LOAD
    Flags:           [ PF_W, PF_R ]
    FirstSec:        .init_array
    LastSec:         .bss
    VAddr:           0x600DA8
    Align:           0x200000
  - Type:            PT_DYNAMIC
    Flags:           [ PF_W, PF_R ]
    FirstSec:        .dynamic
    LastSec:         .dynamic
    VAddr:           0x600DB8
    Align:           0x8
  - Type:            PT_NOTE
    Flags:           [ PF_R ]
    FirstSec:        .note.ABI-tag
    LastSec:         .note.ABI-tag
    VAddr:           0x400254
    Align:           0x4
  - Type:            PT_GNU_EH_FRAME
    Flags:           [ PF_R ]
    FirstSec:        .eh_frame_hdr
    LastSec:         .eh_frame_hdr
    VAddr:           0x4006D8
    Align:           0x4
  - Type:            PT_GNU_STACK
    Flags:           [ PF_W, PF_R ]
    Align:           0x10
  - Type:            PT_GNU_RELRO
    Flags:           [ PF_R ]
    FirstSec:        .init_array
    LastSec:         .got
    VAddr:           0x600DA8
Sections:
  - Name:            .interp
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x400238
    AddressAlign:    0x1
    Content:         2F6C696236342F6C642D6C696E75782D7838362D36342E736F2E3200
  - Name:            .note.ABI-tag
    Type:            SHT_NOTE
    Flags:           [ SHF_ALLOC ]
    Address:         0x400254
    AddressAlign:    0x4
    Notes:
      - Name:            GNU
        Desc:            '00000000030000000200000000000000'
        Type:            NT_VERSION
  - Name:            .hash
    Type:            SHT_HASH
    Flags:           [ SHF_ALLOC ]
    Address:         0x400278
    Link:            .dynsym
    AddressAlign:    0x8
    Bucket:          [ 4, 6, 2 ]
    Chain:           [ 0, 0, 0, 1, 0, 3, 5 ]
  - Name:            .gnu.hash
    Type:            SHT_GNU_HASH
    Flags:           [ SHF_ALLOC ]
    Address:         0x4002A8
    Link:            .dynsym
    AddressAlign:    0x8
    Header:
      SymNdx:          0x1
      Shift2:          0x0
    BloomFilter:     [ 0x0 ]
    HashBuckets:     [ 0x0 ]
    HashValues:      [  ]
  - Name:            .dynsym
    Type:            SHT_DYNSYM
    Flags:           [ SHF_ALLOC ]
    Address:         0x4002C8
    Link:            .dynstr
    AddressAlign:    0x8
  - Name:            .dynstr
    Type:            SHT_STRTAB
    Flags:           [ SHF_ALLOC ]
    Address:         0x400370
    AddressAlign:    0x1
  - Name:            .gnu.version
    Type:            SHT_GNU_versym
    Flags:           [ SHF_ALLOC ]
    Address:         0x4003EA
    Link:            .dynsym
    AddressAlign:    0x2
    Entries:         [ 0, 0, 2, 2, 0, 2, 0 ]
  - Name:            .gnu.version_r
    Type:            SHT_GNU_verneed
    Flags:           [ SHF_ALLOC ]
    Address:         0x4003F8
    Link:            .dynstr
    AddressAlign:    0x8
    Dependencies:
      - Version:         1
        File:            libc.so.6
        Entries:
          - Name:            GLIBC_2.2.5
            Hash:            157882997
            Flags:           0
            Other:           2
  - Name:            .rela.dyn
    Type:            SHT_RELA
    Flags:           [ SHF_ALLOC ]
    Address:         0x400418
    Link:            .dynsym
    AddressAlign:    0x8
    Relocations:
      - Offset:          0x600FE0
        Symbol:          _ITM_deregisterTMCloneTable
        Type:            R_X86_64_GLOB_DAT
      - Offset:          0x600FE8
        Symbol:          __libc_start_main
        Type:            R_X86_64_GLOB_DAT
      - Offset:          0x600FF0
        Symbol:          __gmon_start__
        Type:            R_X86_64_GLOB_DAT
      - Offset:          0x600FF8
        Symbol:          _ITM_registerTMCloneTable
        Type:            R_X86_64_GLOB_DAT
  - Name:            .rela.plt
    Type:            SHT_RELA
    Flags:           [ SHF_ALLOC, SHF_INFO_LINK ]
    Address:         0x400478
    Link:            .dynsym
    AddressAlign:    0x8
    Info:            .got
    Relocations:
      - Offset:          0x600FD0
        Symbol:          printf
        Type:            R_X86_64_JUMP_SLOT
      - Offset:          0x600FD8
        Symbol:          exit
        Type:            R_X86_64_JUMP_SLOT
  - Name:            .init
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x4004A8
    AddressAlign:    0x4
    Content:         F30F1EFA4883EC08488B05390B20004885C07402FFD04883C408C3
  - Name:            .plt
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x4004D0
    AddressAlign:    0x10
    EntSize:         0x10
    Content:         FF35EA0A2000F2FF25EB0A20000F1F006800000000F2E9E5FFFFFF0F1F4400006801000000F2E9D5FFFFFF0F1F440000
  - Name:            .plt.sec
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x400500
    AddressAlign:    0x8
    EntSize:         0x8
    Content:         F2FF25C90A200090F2FF25C90A200090
  - Name:            .text
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x400510
    AddressAlign:    0x10
    Content:         F30F1EFA31ED4989D15E4889E24883E4F0505449C7C0A006400048C7C13006400048C7C700064000FF15AA0A2000F490F30F1EFAC3662E0F1F84000000000090488D3DB10A2000488D05AA0A20004839F87415488B05760A20004885C07409FFE00F1F8000000000C30F1F8000000000488D3D810A2000488D357A0A20004829FE48C1FE034889F048C1E83F4801C648D1FE7414488B054D0A20004885C07408FFE0660F1F440000C30F1F8000000000F30F1EFA803D390A2000007513554889E5E87AFFFFFFC605270A2000015DC390C366662E0F1F8400000000000F1F4000F30F1EFAEB8A662E0F1F840000000000554889E54883EC10C745FC0000000048BFC806400000000000B000E8E0FEFFFF31FFE8E1FEFFFF660F1F840000000000F30F1EFA41574989D741564989F641554189FD41544C8D255C07200055488D2D5C072000534C29E54883EC08E847FEFFFF48C1FD03741F31DB0F1F80000000004C89FA4C89F64489EF41FF14DC4883C3014839DD75EA4883C4085B5D415C415D415E415FC366662E0F1F840000000000F30F1EFAC3
  - Name:            .fini
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x4006A8
    AddressAlign:    0x4
    Content:         F30F1EFA4883EC084883C408C3
  - Name:            .rodata
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x4006B8
    AddressAlign:    0x8
    Content:         0100020000000000000000000000000048656C6C6F20776F726C64210A00
  - Name:            .eh_frame_hdr
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x4006D8
    AddressAlign:    0x4
    Content:         011B033B4400000007000000F8FDFFFF8800000028FEFFFFB000000038FEFFFF6000000068FEFFFF7400000028FFFFFFC800000058FFFFFFE8000000C8FFFFFF30010000
  - Name:            .eh_frame
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x400720
    AddressAlign:    0x8
    Content:         1400000000000000017A5200017810011B0C070890010000100000001C000000D0FDFFFF2F000000004407101000000030000000ECFDFFFF0500000000000000240000004400000068FDFFFF30000000000E10460E184A0F0B770880003F1A352A33242200000000140000006C00000070FDFFFF1000000000000000000000001C0000008400000058FEFFFF2700000000410E108602430D060000000000000044000000A400000068FEFFFF6500000000460E108F02450E188E03450E208D04450E288C05480E308606480E388307470E406E0E38410E30410E28420E20420E18420E10420E080010000000EC00000090FEFFFF050000000000000000000000
  - Name:            .init_array
    Type:            SHT_INIT_ARRAY
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x600DA8
    AddressAlign:    0x8
    EntSize:         0x8
    Offset:          0xDA8
    Content:         F005400000000000
  - Name:            .fini_array
    Type:            SHT_FINI_ARRAY
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x600DB0
    AddressAlign:    0x8
    EntSize:         0x8
    Content:         C005400000000000
  - Name:            .dynamic
    Type:            SHT_DYNAMIC
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x600DB8
    Link:            .dynstr
    AddressAlign:    0x8
    Entries:
      - Tag:             DT_NEEDED
        Value:           0x1
      - Tag:             DT_INIT
        Value:           0x4004A8
      - Tag:             DT_FINI
        Value:           0x4006A8
      - Tag:             DT_INIT_ARRAY
        Value:           0x600DA8
      - Tag:             DT_INIT_ARRAYSZ
        Value:           0x8
      - Tag:             DT_FINI_ARRAY
        Value:           0x600DB0
      - Tag:             DT_FINI_ARRAYSZ
        Value:           0x8
      - Tag:             DT_HASH
        Value:           0x400278
      - Tag:             DT_GNU_HASH
        Value:           0x4002A8
      - Tag:             DT_STRTAB
        Value:           0x400370
      - Tag:             DT_SYMTAB
        Value:           0x4002C8
      - Tag:             DT_STRSZ
        Value:           0x7A
      - Tag:             DT_SYMENT
        Value:           0x18
      - Tag:             DT_DEBUG
        Value:           0x0
      - Tag:             DT_PLTGOT
        Value:           0x600FB8
      - Tag:             DT_PLTRELSZ
        Value:           0x30
      - Tag:             DT_PLTREL
        Value:           0x7
      - Tag:             DT_JMPREL
        Value:           0x400478
      - Tag:             DT_RELA
        Value:           0x400418
      - Tag:             DT_RELASZ
        Value:           0x60
      - Tag:             DT_RELAENT
        Value:           0x18
      - Tag:             DT_BIND_NOW
        Value:           0x0
      - Tag:             DT_FLAGS_1
        Value:           0x1
      - Tag:             DT_VERNEED
        Value:           0x4003F8
      - Tag:             DT_VERNEEDNUM
        Value:           0x1
      - Tag:             DT_VERSYM
        Value:           0x4003EA
      - Tag:             DT_NULL
        Value:           0x0
      - Tag:             DT_NULL
        Value:           0x0
      - Tag:             DT_NULL
        Value:           0x0
      - Tag:             DT_NULL
        Value:           0x0
      - Tag:             DT_NULL
        Value:           0x0
      - Tag:             DT_NULL
        Value:           0x0
  - Name:            .got
    Type:            SHT_PROGBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x600FB8
    AddressAlign:    0x8
    EntSize:         0x8
    Content:         B80D60000000000000000000000000000000000000000000E004400000000000F0044000000000000000000000000000000000000000000000000000000000000000000000000000
  - Name:            .data
    Type:            SHT_PROGBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x601000
    AddressAlign:    0x1
    Content:         '00000000'
  - Name:            .bss
    Type:            SHT_NOBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x601004
    AddressAlign:    0x1
    Size:            0x4
  - Name:            .comment
    Type:            SHT_PROGBITS
    Flags:           [ SHF_MERGE, SHF_STRINGS ]
    AddressAlign:    0x1
    EntSize:         0x1
    Content:         4743433A2028474E552920382E342E3120323032303039323820285265642048617420382E342E312D3129004743433A2028474E552920382E352E3020323032313035313420285265642048617420382E352E302D312900636C616E672076657273696F6E2031322E302E323032313036313000
  - Name:            .gnu.build.attributes
    Type:            SHT_NOTE
    Address:         0xA01008
    AddressAlign:    0x4
    Notes:
      - Name:            "GA$\x013p965"
        Desc:            3F054000000000003F05400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '40054000000000004505400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            '40054000000000004505400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\0"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0�"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            10054000000000003F05400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            3F054000000000003F05400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            '45054000000000004505400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            A804400000000000BE04400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            A806400000000000B006400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            5005400000000000F605400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            3006400000000000A506400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            '30064000000000009506400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            "GA*cf_protection\0\b"
        Desc:            9506400000000000A506400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_FUNC
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013p965"
        Desc:            '10054000000000001005400000000000'
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05running gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x05annobin gcc 8.4.1 20200928"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*GOW\0*E\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x02\x03"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+stack_clash'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*cf_protection\0\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*FORTIFY\0\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+GLIBCXX_ASSERTIONS'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\a\x02"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA!\b"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA+omit_frame_pointer'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA*\x06\x12"
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            'GA!stack_realign'
        Desc:            ''
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            A506400000000000A506400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            A506400000000000A506400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            BE04400000000000C304400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
      - Name:            "GA$\x013a1"
        Desc:            B006400000000000B506400000000000
        Type:            NT_GNU_BUILD_ATTRIBUTE_OPEN
Symbols:
  - Name:            .interp
    Type:            STT_SECTION
    Section:         .interp
    Value:           0x400238
  - Name:            .note.ABI-tag
    Type:            STT_SECTION
    Section:         .note.ABI-tag
    Value:           0x400254
  - Name:            .hash
    Type:            STT_SECTION
    Section:         .hash
    Value:           0x400278
  - Name:            .gnu.hash
    Type:            STT_SECTION
    Section:         .gnu.hash
    Value:           0x4002A8
  - Name:            .dynsym
    Type:            STT_SECTION
    Section:         .dynsym
    Value:           0x4002C8
  - Name:            .dynstr
    Type:            STT_SECTION
    Section:         .dynstr
    Value:           0x400370
  - Name:            .gnu.version
    Type:            STT_SECTION
    Section:         .gnu.version
    Value:           0x4003EA
  - Name:            .gnu.version_r
    Type:            STT_SECTION
    Section:         .gnu.version_r
    Value:           0x4003F8
  - Name:            .rela.dyn
    Type:            STT_SECTION
    Section:         .rela.dyn
    Value:           0x400418
  - Name:            .rela.plt
    Type:            STT_SECTION
    Section:         .rela.plt
    Value:           0x400478
  - Name:            .init
    Type:            STT_SECTION
    Section:         .init
    Value:           0x4004A8
  - Name:            .plt
    Type:            STT_SECTION
    Section:         .plt
    Value:           0x4004D0
  - Name:            .plt.sec
    Type:            STT_SECTION
    Section:         .plt.sec
    Value:           0x400500
  - Name:            .text
    Type:            STT_SECTION
    Section:         .text
    Value:           0x400510
  - Name:            .fini
    Type:            STT_SECTION
    Section:         .fini
    Value:           0x4006A8
  - Name:            .rodata
    Type:            STT_SECTION
    Section:         .rodata
    Value:           0x4006B8
  - Name:            .eh_frame_hdr
    Type:            STT_SECTION
    Section:         .eh_frame_hdr
    Value:           0x4006D8
  - Name:            .eh_frame
    Type:            STT_SECTION
    Section:         .eh_frame
    Value:           0x400720
  - Name:            .init_array
    Type:            STT_SECTION
    Section:         .init_array
    Value:           0x600DA8
  - Name:            .fini_array
    Type:            STT_SECTION
    Section:         .fini_array
    Value:           0x600DB0
  - Name:            .dynamic
    Type:            STT_SECTION
    Section:         .dynamic
    Value:           0x600DB8
  - Name:            .got
    Type:            STT_SECTION
    Section:         .got
    Value:           0x600FB8
  - Name:            .data
    Type:            STT_SECTION
    Section:         .data
    Value:           0x601000
  - Name:            .bss
    Type:            STT_SECTION
    Section:         .bss
    Value:           0x601004
  - Name:            .comment
    Type:            STT_SECTION
    Section:         .comment
  - Name:            .gnu.build.attributes
    Type:            STT_SECTION
    Section:         .gnu.build.attributes
    Value:           0xA01008
  - Name:            '/usr/lib/gcc/x86_64-redhat-linux/8/../../../../lib64/crt1.o'
    Type:            STT_FILE
    Index:           SHN_ABS
  - Name:            .annobin_init.c
    Section:         .text
    Value:           0x40053F
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c_end
    Section:         .text
    Value:           0x40053F
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c.hot
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c_end.hot
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c.unlikely
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c_end.unlikely
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c.startup
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c_end.startup
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c.exit
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_init.c_end.exit
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c
    Section:         .text
    Value:           0x400540
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c_end
    Section:         .text
    Value:           0x400545
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c.hot
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c_end.hot
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c.unlikely
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c_end.unlikely
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c.startup
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c_end.startup
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c.exit
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_static_reloc.c_end.exit
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin__dl_relocate_static_pie.start
    Section:         .text
    Value:           0x400540
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin__dl_relocate_static_pie.end
    Section:         .text
    Value:           0x400545
    Other:           [ STV_HIDDEN ]
  - Name:            elf-init.oS
    Type:            STT_FILE
    Index:           SHN_ABS
  - Name:            .annobin_elf_init.c
    Section:         .text
    Value:           0x400630
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c_end
    Section:         .text
    Value:           0x4006A5
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c.hot
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c_end.hot
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c.unlikely
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c_end.unlikely
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c.startup
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c_end.startup
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c.exit
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin_elf_init.c_end.exit
    Section:         .text
    Value:           0x400510
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin___libc_csu_init.start
    Section:         .text
    Value:           0x400630
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin___libc_csu_init.end
    Section:         .text
    Value:           0x400695
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin___libc_csu_fini.start
    Section:         .text
    Value:           0x400695
    Other:           [ STV_HIDDEN ]
  - Name:            .annobin___libc_csu_fini.end
    Section:         .text
    Value:           0x4006A5
    Other:           [ STV_HIDDEN ]
  - Name:            crtstuff.c
    Type:            STT_FILE
    Index:           SHN_ABS
  - Name:            deregister_tm_clones
    Type:            STT_FUNC
    Section:         .text
    Value:           0x400550
  - Name:            register_tm_clones
    Type:            STT_FUNC
    Section:         .text
    Value:           0x400580
  - Name:            __do_global_dtors_aux
    Type:            STT_FUNC
    Section:         .text
    Value:           0x4005C0
  - Name:            completed.7294
    Type:            STT_OBJECT
    Section:         .bss
    Value:           0x601004
    Size:            0x1
  - Name:            __do_global_dtors_aux_fini_array_entry
    Type:            STT_OBJECT
    Section:         .fini_array
    Value:           0x600DB0
  - Name:            frame_dummy
    Type:            STT_FUNC
    Section:         .text
    Value:           0x4005F0
  - Name:            __frame_dummy_init_array_entry
    Type:            STT_OBJECT
    Section:         .init_array
    Value:           0x600DA8
  - Name:            hello.c
    Type:            STT_FILE
    Index:           SHN_ABS
  - Name:            'crtstuff.c (1)'
    Type:            STT_FILE
    Index:           SHN_ABS
  - Name:            __FRAME_END__
    Type:            STT_OBJECT
    Section:         .eh_frame
    Value:           0x40081C
  - Type:            STT_FILE
    Index:           SHN_ABS
  - Name:            __init_array_end
    Section:         .init_array
    Value:           0x600DB0
  - Name:            _DYNAMIC
    Type:            STT_OBJECT
    Section:         .dynamic
    Value:           0x600DB8
  - Name:            __init_array_start
    Section:         .init_array
    Value:           0x600DA8
  - Name:            __GNU_EH_FRAME_HDR
    Section:         .eh_frame_hdr
    Value:           0x4006D8
  - Name:            _GLOBAL_OFFSET_TABLE_
    Type:            STT_OBJECT
    Section:         .got
    Value:           0x600FB8
  - Name:            __libc_csu_fini
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x4006A0
    Size:            0x5
  - Name:            _ITM_deregisterTMCloneTable
    Binding:         STB_WEAK
  - Name:            data_start
    Section:         .data
    Binding:         STB_WEAK
    Value:           0x601000
  - Name:            _edata
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x601004
  - Name:            _fini
    Type:            STT_FUNC
    Section:         .fini
    Binding:         STB_GLOBAL
    Value:           0x4006A8
    Other:           [ STV_HIDDEN ]
  - Name:            'printf@@GLIBC_2.2.5'
    Type:            STT_FUNC
    Binding:         STB_GLOBAL
  - Name:            '__libc_start_main@@GLIBC_2.2.5'
    Type:            STT_FUNC
    Binding:         STB_GLOBAL
  - Name:            __data_start
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x601000
  - Name:            __gmon_start__
    Binding:         STB_WEAK
  - Name:            __dso_handle
    Type:            STT_OBJECT
    Section:         .rodata
    Binding:         STB_GLOBAL
    Value:           0x4006C0
    Other:           [ STV_HIDDEN ]
  - Name:            _IO_stdin_used
    Type:            STT_OBJECT
    Section:         .rodata
    Binding:         STB_GLOBAL
    Value:           0x4006B8
    Size:            0x4
  - Name:            __libc_csu_init
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x400630
    Size:            0x65
  - Name:            _end
    Section:         .bss
    Binding:         STB_GLOBAL
    Value:           0x601008
  - Name:            _dl_relocate_static_pie
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x400540
    Size:            0x5
    Other:           [ STV_HIDDEN ]
  - Name:            _start
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x400510
    Size:            0x2F
  - Name:            __bss_start
    Section:         .bss
    Binding:         STB_GLOBAL
    Value:           0x601004
  - Name:            main
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x400600
    Size:            0x27
  - Name:            'exit@@GLIBC_2.2.5'
    Type:            STT_FUNC
    Binding:         STB_GLOBAL
  - Name:            __TMC_END__
    Type:            STT_OBJECT
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x601008
    Other:           [ STV_HIDDEN ]
  - Name:            _ITM_registerTMCloneTable
    Binding:         STB_WEAK
  - Name:            _init
    Type:            STT_FUNC
    Section:         .init
    Binding:         STB_GLOBAL
    Value:           0x4004A8
    Other:           [ STV_HIDDEN ]
DynamicSymbols:
  - Name:            _ITM_deregisterTMCloneTable
    Binding:         STB_WEAK
  - Name:            printf
    Type:            STT_FUNC
    Binding:         STB_GLOBAL
  - Name:            __libc_start_main
    Type:            STT_FUNC
    Binding:         STB_GLOBAL
  - Name:            __gmon_start__
    Binding:         STB_WEAK
  - Name:            exit
    Type:            STT_FUNC
    Binding:         STB_GLOBAL
  - Name:            _ITM_registerTMCloneTable
    Binding:         STB_WEAK
...
