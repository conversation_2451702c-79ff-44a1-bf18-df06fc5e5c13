# REQUIRES: system-linux

# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5_main.s -o %tmain.o
# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5_helper.s -o %thelper.o
# RUN: %clang %cflags -dwarf-5 %tmain.o %thelper.o -o %t.exe -Wl,-q
# RUN: llvm-bolt --always-convert-to-ranges %t.exe -o %t.bolt --update-debug-sections --debug-thread-count=4 --cu-processing-batch-size=4
# RUN: llvm-dwarfdump --show-form --verbose --debug-info %t.exe | FileCheck --check-prefix=PRECHECK %s
# RUN: llvm-dwarfdump --show-form --verbose --debug-addr %t.bolt > %t.txt
# RUN: llvm-dwarfdump --show-form --verbose --debug-info %t.bolt >> %t.txt
# RUN: cat %t.txt | FileCheck --check-prefix=POSTCHECK %s

## This tests checks that re-writing of .debug_rnglists is handled correctly for two CUs,
## and DW_AT_low_pc/DW_AT_high_pc conversion is handled correctly.

# PRECHECK: version = 0x0005
# PRECHECK: DW_AT_low_pc [DW_FORM_addrx]
# PRECHECK: DW_AT_high_pc [DW_FORM_data4]
# PRECHECK: DW_AT_loclists_base [DW_FORM_sec_offset] (0x0000000c)
# PRECHECK-EMPTY:
# PRECHECK: version = 0x0005
# PRECHECK: DW_AT_low_pc [DW_FORM_addrx]
# PRECHECK: DW_AT_high_pc [DW_FORM_data4]
# PRECHECK: DW_AT_loclists_base [DW_FORM_sec_offset]  (0x00000043)

# PRECHECK: DW_TAG_inlined_subroutine
# PRECHECK-NEXT: DW_AT_abstract_origin
# PRECHECK-NEXT: DW_AT_low_pc [DW_FORM_addrx]
# PRECHECK-NEXT: DW_AT_high_pc [DW_FORM_data4] (0x00000003)

# POSTCHECK: Addrs: [
# POSTCHECK-NEXT: 0x[[#%.16x,ADDR:]]
# POSTCHECK-NEXT: 0x
# POSTCHECK-NEXT: 0x[[#%.16x,ADDR1:]]
# POSTCHECK-NEXT: 0x[[#%.16x,ADDR2:]]
# For second CU.
# POSTCHECK: Addrs: [
# POSTCHECK-NEXT: 0x[[#%.16x,ADDR3:]]
# POSTCHECK-NEXT: 0x
# POSTCHECK-NEXT: 0x
# POSTCHECK-NEXT: 0x[[#%.16x,ADDR4:]]

# POSTCHECK: version = 0x0005
# POSTCHECK: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: (indexed (0x0)
# POSTCHECK-SAME: rangelist = 0x00000018
# POSTCHECK-NEXT: [
# POSTCHECK-SAME: 0x[[#ADDR]]
# POSTCHECK-SAME: 0x[[#ADDR + 7]]
# POSTCHECK-NEXT: [
# POSTCHECK-SAME: 0x[[#ADDR1]]
# POSTCHECK-SAME: 0x[[#ADDR1 + 12]]
# POSTCHECK-NEXT: DW_AT_addr_base [DW_FORM_sec_offset]  (0x00000008)
# POSTCHECK-NEXT: DW_AT_loclists_base [DW_FORM_sec_offset]	(0x0000000c)
# POSTCHECK-NEXT: DW_AT_rnglists_base [DW_FORM_sec_offset]	(0x0000000c)
# POSTCHECK-EMPTY:
# POSTCHECK: DW_TAG_subprogram
# POSTCHECK-NEXT: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: indexed (0x1)
# POSTCHECK-SAME: rangelist = 0x00000021
# POSTCHECK-NEXT: [0x[[#ADDR]], 0x[[#ADDR + 7]])

# POSTCHECK: DW_TAG_subprogram
# POSTCHECK-NEXT: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: indexed (0x2)
# POSTCHECK-SAME: rangelist = 0x00000025
# POSTCHECK-NEXT: [0x[[#ADDR1]], 0x[[#ADDR1 + 12]])

# Checking second CU
# POSTCHECK: version = 0x0005
# POSTCHECK: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: (indexed (0x0)
# POSTCHECK-SAME: rangelist = 0x00000045
# POSTCHECK-NEXT: [0x[[#ADDR3]], 0x[[#ADDR3 + 4]])
# POSTCHECK-NEXT: [0x[[#ADDR4]], 0x[[#ADDR4 + 4]])
# POSTCHECK-NEXT: DW_AT_addr_base [DW_FORM_sec_offset]  (0x00000030)
# POSTCHECK-NEXT: DW_AT_loclists_base [DW_FORM_sec_offset]	(0x00000045)
# POSTCHECK-NEXT: DW_AT_rnglists_base [DW_FORM_sec_offset]	(0x00000035)
# POSTCHECK-EMPTY:

# POSTCHECK: DW_TAG_subprogram
# POSTCHECK-NEXT: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: indexed (0x1)
# POSTCHECK-SAME: rangelist = 0x0000004e
# POSTCHECK-NEXT: [0x[[#ADDR3]], 0x[[#ADDR3 + 4]])
# POSTCHECK: DW_TAG_subprogram
# POSTCHECK: DW_TAG_subprogram
# POSTCHECK-NEXT: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: indexed (0x2)
# POSTCHECK-SAME: rangelist = 0x00000052
# POSTCHECK-NEXT: [0x[[#ADDR4]], 0x[[#ADDR4 + 4]])

# POSTCHECK: DW_TAG_inlined_subroutine
# POSTCHECK-NEXT: DW_AT_abstract_origin
# POSTCHECK-NEXT: DW_AT_ranges [DW_FORM_rnglistx]
# POSTCHECK-SAME: indexed (0x3)
# POSTCHECK-SAME: rangelist = 0x00000056
# POSTCHECK-NEXT: [0x[[#ADDR4]], 0x[[#ADDR4 + 3]])
