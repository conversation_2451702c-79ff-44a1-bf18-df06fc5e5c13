# REQUIRES: system-linux

# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-gdb-index-types-main.s -o %tmain.o
# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-gdb-index-types-helper.s -o %thelper.o
# RUN: %clang %cflags %tmain.o %thelper.o -o %t.exe -Wl,-q -Wl,--gdb-index -nostdlib
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections
# RUN: llvm-dwarfdump --gdb-index %t.bolt | FileCheck --check-prefix=POSTCHECK %s

## Tests that BOL<PERSON> correctly handles gdb-index generated by LLD.

# POSTCHECK:          Version = 7
# POSTCHECK:          CU list offset = 0x18, has 2 entries
# POSTCHECK-NEXT:         0: Offset = 0x0, Length = 0x6e
# POSTCHECK-NEXT:         1: Offset = 0x6e, Length = 0x72
# POSTCHECK:          Types CU list offset = 0x38, has 0 entries
# POSTCHECK:          Address area offset = 0x38, has 2 entries
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR:]],
# POSTCHECK-SAME:           0x[[#ADDR + 0xf]]) (Size: 0xf), CU id = 0
# POSTCHECK-NEXT:         Low/High address = [0x[[#%.4x,ADDR1:]],
# POSTCHECK-SAME:           0x[[#ADDR1 + 0xd]]) (Size: 0xd), CU id = 1
# POSTCHECK:          Symbol table offset = 0x60, size = 1024, filled slots
# POSTCHECK-NEXT:         2: Name offset = 0x38, CU vector offset = 0x0
# POSTCHECK-NEXT:             String name: S, CU vector index: 0
# POSTCHECK-NEXT:         71: Name offset = 0x3a, CU vector offset = 0x8
# POSTCHECK-NEXT:             String name: S2, CU vector index: 1
# POSTCHECK-NEXT:         489: Name offset = 0x4a, CU vector offset = 0x1c
# POSTCHECK-NEXT:             String name: main, CU vector index: 3
# POSTCHECK-NEXT:         661: Name offset = 0x53, CU vector offset = 0x30
# POSTCHECK-NEXT:             String name: foo, CU vector index: 5
# POSTCHECK-NEXT:         732: Name offset = 0x3d, CU vector offset = 0x10
# POSTCHECK-NEXT:             String name: unsigned int, CU vector index: 2
# POSTCHECK-NEXT:         754: Name offset = 0x4f, CU vector offset = 0x24
# POSTCHECK-NEXT:             String name: int, CU vector index: 4
# POSTCHECK:          Constant pool offset = 0x2060, has 6 CU vectors
# POSTCHECK-NEXT:         0(0x0): 0x90000000
# POSTCHECK-NEXT:         1(0x8): 0x90000001
# POSTCHECK-NEXT:         2(0x10): 0x10000000 0x10000001
# POSTCHECK-NEXT:         3(0x1c): 0x30000000
# POSTCHECK-NEXT:         4(0x24): 0x90000000 0x90000001
# POSTCHECK-NEXT:         5(0x30): 0x30000001
