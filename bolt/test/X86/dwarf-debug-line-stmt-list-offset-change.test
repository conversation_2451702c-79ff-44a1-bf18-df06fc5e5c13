# REQUIRES: system-linux

## Check that BOL<PERSON> updates DW_AT_stmt_list correctly for TUs when both objects are built with DWARF5.

# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5-debug-line-offset-change-after-bolt-main.s -o %tmain.o
# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-types-dwarf5-types-helper.s -o %thelper.o
# RUN: %clang %cflags %tmain.o %thelper.o -o %t.exe -Wl,-q
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections --lite=0 --reorder-blocks=reverse
# RUN: llvm-dwarfdump --debug-info -r 0 %t.exe > %tlogOffsetNotEqual.txt
# RUN: llvm-dwarfdump --debug-info -r 0 %t.bolt >> %tlogOffsetNotEqual.txt
# RUN: llvm-dwarfdump --debug-info -r 0 --debug-line %t.bolt > %tlogCheckLineTable.txt
# RUN: cat %tlogOffsetNotEqual.txt | FileCheck --check-prefix=CHECK1 %s
# RUN: cat %tlogCheckLineTable.txt | FileCheck --check-prefix=CHECK2 %s

## Check offset gets modified.
# CHECK1: DW_TAG_compile_unit
# CHECK1: DW_AT_stmt_list
# CHECK1: DW_TAG_type_unit
# CHECK1: DW_AT_stmt_list	([[OFFSET:0x[0-9a-f]*]])
# CHECK1: DW_TAG_type_unit
# CHECK1: DW_AT_stmt_list	([[OFFSET]])
# CHECK1: DW_TAG_compile_unit
# CHECK1: DW_AT_stmt_list	([[OFFSET]])
# CHECK1: DW_TAG_type_unit
# CHECK1-NOT: DW_AT_stmt_list	([[OFFSET]])
# CHECK1: DW_TAG_type_unit
# CHECK1-NOT: DW_AT_stmt_list	([[OFFSET]])
# CHECK1: DW_TAG_compile_unit
# CHECK1: DW_TAG_compile_unit
# CHECK1-NOT: DW_AT_stmt_list	([[OFFSET]])

## Check that offset is correct.
# CHECK2: DW_TAG_type_unit
# CHECK2: DW_AT_stmt_list	([[OFFSET1:0x[0-9a-f]*]])
# CHECK2: DW_TAG_type_unit
# CHECK2: DW_AT_stmt_list	([[OFFSET1]])
# CHECK2: DW_TAG_compile_unit
# CHECK2: DW_AT_stmt_list
# CHECK2: DW_TAG_compile_unit
# CHECK2: DW_AT_stmt_list	([[OFFSET1]])
# CHECK2: debug_line
# CHECK2: debug_line[[[OFFSET1]]]
# CHECK2-NOT: debug_line

## Check that BOLT updates DW_AT_stmt_list correctly for TUs when both objects are built with DWARF4.

# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-debug-line-offset-change-after-bolt-main.s -o %tmain.o
# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-debug-line-offset-change-after-bolt-helper.s -o %thelper.o
# RUN: %clang %cflags %tmain.o %thelper.o -o %t.exe -Wl,-q
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections --lite=0 --reorder-blocks=reverse
# RUN: llvm-dwarfdump --debug-info --debug-types -r 0 %t.exe > %tlogOffsetNotEqual.txt
# RUN: llvm-dwarfdump --debug-info --debug-types -r 0 %t.bolt >> %tlogOffsetNotEqual.txt
# RUN: llvm-dwarfdump --debug-info --debug-types -r 0 --debug-line %t.bolt > %tlogCheckLineTable.txt
# RUN: cat %tlogOffsetNotEqual.txt | FileCheck --check-prefix=CHECK3 %s
# RUN: cat %tlogCheckLineTable.txt | FileCheck --check-prefix=CHECK4 %s
## Check offset gets modified.
# CHECK3: DW_TAG_compile_unit
# CHECK3: DW_TAG_compile_unit
# CHECK3: DW_AT_stmt_list ([[OFFSET2:0x[0-9a-f]*]])
# CHECK3: DW_TAG_type_unit
# CHECK3: DW_AT_stmt_list ([[OFFSET2]])
# CHECK3: DW_TAG_compile_unit
# CHECK3-NOT: DW_AT_stmt_list ([[OFFSET2]])
# CHECK3: DW_TAG_type_unit
# CHECK3-NOT: DW_AT_stmt_list ([[OFFSET2]])

## Check that offset is correct.
# CHECK4: DW_TAG_compile_unit
# CHECK4: DW_TAG_compile_unit
# CHECK4: DW_TAG_type_unit
# CHECK4: DW_AT_stmt_list ([[OFFSET3:0x[0-9a-f]*]])
# CHECK4: debug_line
# CHECK4: debug_line[[[OFFSET3]]]
# CHECK4-NOT: debug_line

## Check that BOLT updates DW_AT_stmt_list correctly for TUs when objects are built with DWARF4/DWARF5.

# RUN: llvm-mc -dwarf-version=4 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-debug-line-offset-change-after-bolt-main.s -o %tmain.o
# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf4-types-dwarf5-types-helper.s -o %thelper.o
# RUN: %clang %cflags %tmain.o %thelper.o -o %t.exe -Wl,-q
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections --lite=0 --reorder-blocks=reverse
# RUN: llvm-dwarfdump --debug-info --debug-types -r 0 %t.exe > %tlogOffsetNotEqual.txt
# RUN: llvm-dwarfdump --debug-info --debug-types -r 0 %t.bolt >> %tlogOffsetNotEqual.txt
# RUN: llvm-dwarfdump --debug-info --debug-types -r 0 --debug-line %t.bolt > %tlogCheckLineTable.txt
# RUN: cat %tlogOffsetNotEqual.txt | FileCheck --check-prefix=CHECK5 %s
# RUN: cat %tlogCheckLineTable.txt | FileCheck --check-prefix=CHECK6 %s

## Check offset gets modified.
# CHECK5: DW_TAG_compile_unit
# CHECK5: DW_TAG_type_unit
# CHECK5: DW_AT_stmt_list ([[OFFSET4:0x[0-9a-f]*]])
# CHECK5: DW_TAG_type_unit
# CHECK5: DW_AT_stmt_list ([[OFFSET4]])
# CHECK5: DW_TAG_compile_unit
# CHECK5: DW_AT_stmt_list ([[OFFSET4]])
# CHECK5: DW_TAG_type_unit
# CHECK5-NOT: DW_AT_stmt_list ([[OFFSET4]])
# CHECK5: DW_TAG_type_unit
# CHECK5-NOT: DW_AT_stmt_list ([[OFFSET4]])
# CHECK5: DW_TAG_compile_unit
# CHECK5: DW_TAG_compile_unit
# CHECK5-NOT: DW_AT_stmt_list ([[OFFSET4]])

# CHECK6: DW_TAG_type_unit
# CHECK6: DW_AT_stmt_list ([[OFFSET5:0x[0-9a-f]*]])
# CHECK6: DW_TAG_type_unit
# CHECK6: DW_AT_stmt_list ([[OFFSET5]])
# CHECK6: DW_TAG_compile_unit
# CHECK6: DW_TAG_compile_unit
# CHECK6: DW_AT_stmt_list ([[OFFSET5]])
# CHECK6: debug_line
# CHECK6: debug_line[[[OFFSET5]]]
# CHECK6-NOT: debug_line
