# REQUIRES: system-linux

# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5_main.s -o %tmain.o
# RUN: llvm-mc -dwarf-version=5 -filetype=obj -triple x86_64-unknown-linux %p/Inputs/dwarf5_helper.s -o %thelper.o
# RUN: %clang %cflags -dwarf-5 %tmain.o %thelper.o -o %t.exe -Wl,-q
# RUN: llvm-bolt %t.exe -o %t.bolt --update-debug-sections --debug-thread-count=4 --cu-processing-batch-size=4
# RUN: llvm-dwarfdump --show-form --verbose --debug-str-offsets %t.exe > %t.txt
# RUN: llvm-dwarfdump --show-form --verbose --debug-str-offsets %t.bolt >> %t.txt
# RUN: cat %t.txt | FileCheck --check-prefix=CHECK %s

## This test checks we correclty re-renerate .debug_str_offsets.

# CHECK: .debug_str_offsets contents
# CHECK-NEXT: 0x00000000: Contribution size = 52, Format = DWARF32, Version = 5
# CHECK-NEXT: "clang version 15.0.0"
# CHECK-NEXT: "main.cpp"
# CHECK-NEXT: "/testLocListMultiple"
# CHECK-NEXT: "_Z3usePiS_"
# CHECK-NEXT: "use"
# CHECK-NEXT: "main"
# CHECK-NEXT: "int"
# CHECK-NEXT: "x"
# CHECK-NEXT: "y"
# CHECK-NEXT: "argc"
# CHECK-NEXT: "argv"
# CHECK-NEXT: "char"
# CHECK-NEXT: 0x00000038: Contribution size = 48, Format = DWARF32, Version = 5
# CHECK-NEXT: "clang version 15.0.0)"
# CHECK-NEXT: "foo.cpp"
# CHECK-NEXT: "/testLocListMultiple"
# CHECK-NEXT: "fooVar"
# CHECK-NEXT: "int"
# CHECK-NEXT: "_Z6useFooPi"
# CHECK-NEXT: "useFoo"
# CHECK-NEXT: "x"
# CHECK-NEXT: "_Z3fooi"
# CHECK-NEXT: "foo"
# CHECK-NEXT: "argc"

## Checking post bolt
# CHECK: .debug_str_offsets contents
# CHECK-NEXT: 0x00000000: Contribution size = 52, Format = DWARF32, Version = 5
# CHECK-NEXT: "clang version 15.0.0"
# CHECK-NEXT: "main.cpp"
# CHECK-NEXT: "/testLocListMultiple"
# CHECK-NEXT: "_Z3usePiS_"
# CHECK-NEXT: "use"
# CHECK-NEXT: "main"
# CHECK-NEXT: "int"
# CHECK-NEXT: "x"
# CHECK-NEXT: "y"
# CHECK-NEXT: "argc"
# CHECK-NEXT: "argv"
# CHECK-NEXT: "char"
# CHECK-NEXT: 0x00000038: Contribution size = 48, Format = DWARF32, Version = 5
# CHECK-NEXT: "clang version 15.0.0)"
# CHECK-NEXT: "foo.cpp"
# CHECK-NEXT: "/testLocListMultiple"
# CHECK-NEXT: "fooVar"
# CHECK-NEXT: "int"
# CHECK-NEXT: "_Z6useFooPi"
# CHECK-NEXT: "useFoo"
# CHECK-NEXT: "x"
# CHECK-NEXT: "_Z3fooi"
# CHECK-NEXT: "foo"
# CHECK-NEXT: "argc"
