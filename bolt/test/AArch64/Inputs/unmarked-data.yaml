--- !ELF
FileHeader:
  Class:           ELFCLASS64
  Data:            ELFDATA2LSB
  Type:            ET_EXEC
  Machine:         EM_AARCH64
  Entry:           0x210134
ProgramHeaders:
  - Type:            PT_PHDR
    Flags:           [ PF_R ]
    VAddr:           0x200040
    Align:           0x8
    FileSize:        0x0000e0
    MemSize:         0x0000e0
    Offset:          0x000040
  - Type:            PT_LOAD
    Flags:           [ PF_R ]
    VAddr:           0x200000
    Align:           0x10000
    FileSize:        0x000120
    MemSize:         0x000120
    Offset:          0x000000
  - Type:            PT_LOAD
    Flags:           [ PF_X, PF_R ]
    FirstSec:        .text
    LastSec:         .text
    VAddr:           0x210120
    Align:           0x10000
  - Type:            PT_GNU_STACK
    Flags:           [ PF_W, PF_R ]
    Align:           0x0
Sections:
  - Name:            .text
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x210120
    AddressAlign:    0x4
    Content:         030F0B0700000000030F0B0700000000C0035FD6FFFFFF97000080D2A80B8052010000D4
  - Name:            .rela.text
    Type:            SHT_RELA
    Flags:           [ SHF_INFO_LINK ]
    Link:            .symtab
    AddressAlign:    0x8
    Info:            .text
    Relocations:
      - Offset:          0x210134
        Symbol:          dummy
        Type:            R_AARCH64_CALL26
  - Name:            .comment
    Type:            SHT_PROGBITS
    Flags:           [ SHF_MERGE, SHF_STRINGS ]
    AddressAlign:    0x1
    EntSize:         0x1
    Content:         4C696E6B65723A204C4C442031352E302E3000
Symbols:
  - Name:            val
    Index:           SHN_ABS
    Value:           0x70B0F03
  - Name:            first
    Section:         .text
    Value:           0x210120
    Size:            0x8
  - Name:            '$d.0'
    Section:         .text
    Value:           0x210120
  - Name:            second
    Section:         .text
    Value:           0x210128
    Size:            0x8
  - Name:            '$x.1'
    Section:         .text
    Value:           0x210130
  - Name:            .text
    Type:            STT_SECTION
    Section:         .text
    Value:           0x210120
  - Name:            .comment
    Type:            STT_SECTION
    Section:         .comment
  - Name:            dummy
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x210130
  - Name:            _start
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x210134
...
