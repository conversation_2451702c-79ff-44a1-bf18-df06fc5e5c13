--- !ELF
FileHeader:
  Class:           ELFCLASS64
  Data:            ELFDATA2LSB
  Type:            ET_DYN
  Machine:         EM_AARCH64
  Entry:           0x590
ProgramHeaders:
  - Type:            PT_LOAD
    Flags:           [ PF_X, PF_R, PF_W ]
    FirstSec:        .dynsym
    LastSec:         .got
    Align:           0x10000
  - Type:            PT_DYNAMIC
    Flags:           [ PF_W, PF_R ]
    FirstSec:        .dynamic
    LastSec:         .dynamic
    VAddr:           0x10DF0
    Align:           0x8
  - Type:            PT_TLS
    Flags:           [ PF_R ]
    FirstSec:        .tbss
    LastSec:         .tbss
    VAddr:           0x10DE0
    Align:           0x4
  - Type:            PT_GNU_EH_FRAME
    Flags:           [ PF_R ]
    FirstSec:        .eh_frame_hdr
    LastSec:         .eh_frame_hdr
    VAddr:           0x6B8
    Align:           0x4
Sections:
  - Name:            .dynsym
    Type:            SHT_DYNSYM
    Flags:           [ SHF_ALLOC ]
    Address:         0x250
    Link:            .dynstr
    AddressAlign:    0x8
  - Name:            .dynstr
    Type:            SHT_STRTAB
    Flags:           [ SHF_ALLOC ]
    Address:         0x340
    AddressAlign:    0x1
  - Name:            .rela.dyn
    Type:            SHT_RELA
    Flags:           [ SHF_ALLOC ]
    Address:         0x400
    Link:            .dynsym
    AddressAlign:    0x8
    Relocations:
      - Offset:          0x10FD0
        Symbol:          t1
        Type:            R_AARCH64_TLS_DTPMOD64
      - Offset:          0x10FD8
        Symbol:          t1
        Type:            R_AARCH64_TLS_DTPREL64
  - Name:            .plt
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x540
    AddressAlign:    0x10
    EntSize:         0x10
    Content:         F07BBFA99000009011FE47F910E23F9120021FD61F2003D51F2003D51F2003D5900000B0110240F91002009120021FD6900000B0110640F91022009120021FD6900000B0110A40F91042009120021FD6
  - Name:            .text
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x590
    AddressAlign:    0x8
    Content:         8000009000F047F9400000B4F9FFFF17C0035FD61F2003D5800000B000800091810000B0218000913F0000EBC00000548100009021E447F9610000B4F00301AA00021FD6C0035FD6800000B000800091810000B021800091210000CB22FC7FD3410C818BFF0781EB21FC4193C00000548200009042E047F9620000B4F00302AA00021FD6C0035FD6FD7BBEA9FD030091F30B00F9930000B060824039400100358000009000DC47F9800000B4800000B0000C40F9C7FFFF97D8FFFF972000805260820039F30B40F9FD7BC2A8C0035FD6DEFFFF171F2003D5FD7BBEA9FD030091F30B00F9F303002A8000009000403F91BCFFFF971F2003D5E10300AA60060011F30B40F9220040B942040011220000B9FD7BC2A8C0035FD6
  - Name:            .eh_frame_hdr
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x6B8
    AddressAlign:    0x4
    Content:         011B033B3400000005000000F0FEFFFF4C00000020FFFFFF6000000060FFFFFF74000000A8FFFFFF98000000B0FFFFFFB0000000
  - Name:            .eh_frame
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC ]
    Address:         0x6F0
    AddressAlign:    0x8
    Content:         1000000000000000017A520004781E011B0C1F0010000000180000009CFEFFFF3000000000000000100000002C000000B8FEFFFF40000000000000002000000040000000E4FEFFFF4800000000410E209D049E034293024EDEDDD30E00000000140000006400000008FFFFFF040000000000000000000000200000007C000000F8FEFFFF4000000000410E209D049E034293024CDEDDD30E0000000000000000
  - Name:            .tbss
    Type:            SHT_NOBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC, SHF_TLS ]
    Address:         0x10DE0
    AddressAlign:    0x4
    Offset:          0xDE0
    Size:            0x4
  - Name:            .dynamic
    Type:            SHT_DYNAMIC
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x10DF0
    Link:            .dynstr
    AddressAlign:    0x8
    Entries:
      - Tag:             DT_STRTAB
        Value:           0x340
      - Tag:             DT_SYMTAB
        Value:           0x250
      - Tag:             DT_RELA
        Value:           0x400
      - Tag:             DT_RELASZ
        Value:           0x30
      - Tag:             DT_RELAENT
        Value:           0x18
      - Tag:             DT_RELACOUNT
        Value:           0x3
      - Tag:             DT_NULL
        Value:           0x0
  - Name:            .got
    Type:            SHT_PROGBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x10FB0
    AddressAlign:    0x8
    EntSize:         0x8
    Content:         F00D010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
  - Name:            .rela.text
    Type:            SHT_RELA
    Flags:           [ SHF_INFO_LINK ]
    Link:            .symtab
    AddressAlign:    0x8
    Info:            .text
    Relocations:
      - Offset:          0x678
        Symbol:          t1
        Type:            R_AARCH64_TLSGD_ADR_PAGE21
      - Offset:          0x67C
        Symbol:          t1
        Type:            R_AARCH64_TLSGD_ADD_LO12_NC
  - Type:            SectionHeaderTable
    Sections:
      - Name:            .dynsym
      - Name:            .dynstr
      - Name:            .rela.dyn
      - Name:            .plt
      - Name:            .text
      - Name:            .rela.text
      - Name:            .eh_frame_hdr
      - Name:            .eh_frame
      - Name:            .tbss
      - Name:            .dynamic
      - Name:            .got
      - Name:            .symtab
      - Name:            .strtab
      - Name:            .shstrtab
Symbols:
  - Name:            .text
    Type:            STT_SECTION
    Section:         .text
    Value:           0x590
  - Name:            t1
    Type:            STT_TLS
    Section:         .tbss
    Binding:         STB_GLOBAL
    Size:            0x4
DynamicSymbols:
  - Name:            t1
    Type:            STT_TLS
    Section:         .tbss
    Binding:         STB_GLOBAL
    Size:            0x4
...
