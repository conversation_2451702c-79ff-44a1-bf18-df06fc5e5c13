--- !ELF
FileHeader:
  Class:           ELFCLASS64
  Data:            ELFDATA2LSB
  Type:            ET_DYN
  Machine:         EM_AARCH64
ProgramHeaders:
  - Type:            PT_PHDR
    Flags:           [ PF_R ]
    VAddr:           0x40
    Align:           0x8
  - Type:            PT_LOAD
    Flags:           [ PF_R ]
    FirstSec:        .dynsym
    LastSec:         .dynamic
    Align:           0x10000
  - Type:            PT_LOAD
    Flags:           [ PF_X, PF_R ]
    FirstSec:        .text
    LastSec:         .tbss
    VAddr:           0x10658
    Align:           0x10000
  - Type:            PT_LOAD
    Flags:           [ PF_W, PF_R ]
    FirstSec:        .tbss
    LastSec:         .got
    VAddr:           0x207F0
    Align:           0x10000
  - Type:            PT_LOAD
    Flags:           [ PF_W, PF_R ]
    FirstSec:        .data
    LastSec:         .bss
    VAddr:           0x309C8
    Align:           0x10000
  - Type:            PT_TLS
    Flags:           [ PF_R ]
    FirstSec:        .tbss
    LastSec:         .tbss
    VAddr:           0x107F0
    Align:           0x4
  - Type:            PT_DYNAMIC
    Flags:           [ PF_W, PF_R ]
    FirstSec:        .dynamic
    LastSec:         .dynamic
    VAddr:           0x20800
    Align:           0x8
  - Type:            PT_GNU_RELRO
    Flags:           [ PF_R ]
    FirstSec:        .tbss
    LastSec:         .got
    VAddr:           0x207F0
Sections:
  - Name:            .dynsym
    Type:            SHT_DYNSYM
    Flags:           [ SHF_ALLOC ]
    Address:         0x270
    Link:            .dynstr
    AddressAlign:    0x8
  - Name:            .dynstr
    Type:            SHT_STRTAB
    Flags:           [ SHF_ALLOC ]
    Address:         0x3AC
    AddressAlign:    0x1
  - Name:            .rela.dyn
    Type:            SHT_RELA
    Flags:           [ SHF_ALLOC ]
    Address:         0x428
    Link:            .dynsym
    AddressAlign:    0x8
    Relocations:
      - Offset:          0x209B0
        Symbol:          a
        Type:            R_AARCH64_GLOB_DAT
      - Offset:          0x309D8
        Symbol:          a
        Type:            R_AARCH64_ABS64
      - Offset:          0x209B8
        Symbol:          t1
        Type:            R_AARCH64_TLSDESC
  - Name:            .rela.plt
    Type:            SHT_RELA
    Flags:           [ SHF_ALLOC, SHF_INFO_LINK ]
    Address:         0x530
    Link:            .dynsym
    AddressAlign:    0x8
    Info:            .got.plt
    Relocations:
      - Offset:          0x30A00
        Symbol:          __gmon_start__
        Type:            R_AARCH64_JUMP_SLOT
      - Offset:          0x30A08
        Symbol:          __cxa_finalize
        Type:            R_AARCH64_JUMP_SLOT
  - Name:            .text
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x10658
    AddressAlign:    0x8
    Content:         8000009000C844F9400000B45B000014C0035FD6000000001F2003D5A01B10101F2003D5611B10103F0000EBC00000548100009021CC44F9610000B4F00301AA00021FD6C0035FD61F2003D5201A10101F2003D5E1191010210000CB22FC7FD3410C818BFF0781EB21FC4193C00000548200009042D044F9620000B4F00302AA00021FD6C0035FD6FD7BBEA9FD030091F30B00F91301009060426839400100358000009000D444F9800000B40001009000E444F935000094D8FFFF972000805260422839F30B40F9FD7BC2A8C0035FD6DEFFFF17FF8300D1FD7B01A9FD430091A0C31FB88900009029D944F9280140B908050011280100B98000009001DC44F900E0269120003FD649D03BD5286960B808050011286920B8A8C35FB800050011FD7B41A9FF830091C0035FD6
  - Name:            .plt
    Type:            SHT_PROGBITS
    Flags:           [ SHF_ALLOC, SHF_EXECINSTR ]
    Address:         0x107B0
    AddressAlign:    0x10
    Content:         F07BBFA91001009011FE44F910E2279120021FD61F2003D51F2003D51F2003D510010090110245F91002289120021FD610010090110645F91022289120021FD6
  - Name:            .tbss
    Type:            SHT_NOBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC, SHF_TLS ]
    Address:         0x107F0
    AddressAlign:    0x4
    Size:            0x4
  - Name:            .dynamic
    Type:            SHT_DYNAMIC
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x20800
    Link:            .dynstr
    AddressAlign:    0x8
    Entries:
      - Tag:             DT_NEEDED
        Value:           0x60
      - Tag:             DT_SONAME
        Value:           0x75
      - Tag:             DT_RELA
        Value:           0x428
      - Tag:             DT_RELASZ
        Value:           0x48
      - Tag:             DT_RELAENT
        Value:           0x18
      - Tag:             DT_RELACOUNT
        Value:           0x4
      - Tag:             DT_JMPREL
        Value:           0x530
      - Tag:             DT_PLTRELSZ
        Value:           0x30
      - Tag:             DT_PLTGOT
        Value:           0x309E8
      - Tag:             DT_PLTREL
        Value:           0x7
      - Tag:             DT_SYMTAB
        Value:           0x270
      - Tag:             DT_SYMENT
        Value:           0x18
      - Tag:             DT_STRTAB
        Value:           0x3AC
      - Tag:             DT_STRSZ
        Value:           0x7C
      - Tag:             DT_GNU_HASH
        Value:           0x380
      - Tag:             DT_NULL
        Value:           0x0
  - Name:            .got
    Type:            SHT_PROGBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x20990
    AddressAlign:    0x8
    Content:         '0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'
  - Name:            .data
    Type:            SHT_PROGBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x309C8
    AddressAlign:    0x8
    Content:         '0000000000000000010000000000000000000000000000000000000000000000'
  - Name:            .got.plt
    Type:            SHT_PROGBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x309E8
    AddressAlign:    0x8
    Content:         000000000000000000000000000000000000000000000000B007010000000000B007010000000000
  - Name:            .bss
    Type:            SHT_NOBITS
    Flags:           [ SHF_WRITE, SHF_ALLOC ]
    Address:         0x30A10
    AddressAlign:    0x4
    Size:            0x8
  - Name:            .rela.text
    Type:            SHT_RELA
    Flags:           [ SHF_INFO_LINK ]
    Link:            .symtab
    AddressAlign:    0x8
    Info:            .text
    Relocations:
      - Offset:          0x1073C
        Symbol:          a
        Type:            R_AARCH64_ADR_GOT_PAGE
      - Offset:          0x10740
        Symbol:          a
        Type:            R_AARCH64_LD64_GOT_LO12_NC
      - Offset:          0x10750
        Symbol:          t1
        Type:            R_AARCH64_TLSDESC_ADR_PAGE21
      - Offset:          0x10754
        Symbol:          t1
        Type:            R_AARCH64_TLSDESC_LD64_LO12
      - Offset:          0x10758
        Symbol:          t1
        Type:            R_AARCH64_TLSDESC_ADD_LO12
      - Offset:          0x1075C
        Symbol:          t1
        Type:            R_AARCH64_TLSDESC_CALL
  - Name:            .rela.data
    Type:            SHT_RELA
    Flags:           [ SHF_INFO_LINK ]
    Link:            .symtab
    AddressAlign:    0x8
    Info:            .data
    Relocations:
      - Offset:          0x309C8
        Symbol:          __dso_handle
        Type:            R_AARCH64_ABS64
      - Offset:          0x309D8
        Symbol:          a
        Type:            R_AARCH64_ABS64
      - Offset:          0x309E0
        Symbol:          .bss
        Type:            R_AARCH64_ABS64
        Addend:          4
Symbols:
  - Name:            '$x'
    Section:         .text
    Value:           0x10658
  - Name:            call_weak_fn
    Type:            STT_FUNC
    Section:         .text
    Value:           0x10658
    Size:            0x14
  - Name:            .text
    Type:            STT_SECTION
    Section:         .text
    Value:           0x10658
  - Name:            .data
    Type:            STT_SECTION
    Section:         .data
    Value:           0x309C8
  - Name:            .bss
    Type:            STT_SECTION
    Section:         .bss
    Value:           0x30A10
  - Name:            frame_dummy
    Type:            STT_FUNC
    Section:         .text
    Value:           0x10728
  - Name:            __dso_handle
    Type:            STT_OBJECT
    Section:         .data
    Value:           0x309C8
    Other:           [ STV_HIDDEN ]
  - Name:            .tbss
    Type:            STT_SECTION
    Section:         .tbss
    Value:           0x107F0
  - Name:            _DYNAMIC
    Section:         .dynamic
    Value:           0x20800
    Other:           [ STV_HIDDEN ]
  - Name:            inc
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x1072C
    Size:            0x58
  - Name:            a
    Type:            STT_OBJECT
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x309D0
    Size:            0x4
  - Name:            t1
    Type:            STT_TLS
    Section:         .tbss
    Binding:         STB_GLOBAL
    Size:            0x4
  - Name:            b
    Type:            STT_OBJECT
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x309D8
    Size:            0x8
DynamicSymbols:
  - Name:            __gmon_start__
    Binding:         STB_WEAK
  - Name:            __cxa_finalize
    Type:            STT_FUNC
    Binding:         STB_WEAK
  - Name:            inc
    Type:            STT_FUNC
    Section:         .text
    Binding:         STB_GLOBAL
    Value:           0x1072C
    Size:            0x58
  - Name:            a
    Type:            STT_OBJECT
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x309D0
    Size:            0x4
  - Name:            t1
    Type:            STT_TLS
    Section:         .tbss
    Binding:         STB_GLOBAL
    Size:            0x4
  - Name:            b
    Type:            STT_OBJECT
    Section:         .data
    Binding:         STB_GLOBAL
    Value:           0x309D8
    Size:            0x8
