# Verify that we update DW_TAG_compile_unit' ranges and .debug_aranges
# for assembly function that doesn't have corresponding DIE.
#
# The input test case foo() contains nops that we remove.

RUN: %clang %cflags -gdwarf-5 -no-pie %p/../Inputs/asm_foo.s %p/../Inputs/asm_main.c -o %t.exe
RUN: llvm-bolt %t.exe -o %t --update-debug-sections
RUN: llvm-dwarfdump -all %t | FileCheck %s

# Check ranges were created/updated for asm compile unit
CHECK:       0x0000000c: DW_TAG_compile_unit
CHECK-NEXT:       DW_AT_stmt_list (0x00000000)
CHECK-NEXT:       DW_AT_low_pc (0x0000000000000000)
CHECK-NEXT:       DW_AT_ranges
CHECK-NEXT:          [0x0000000000[[#%x,ADDR:]],
CHECK-SAME:                              0x0000000000[[#ADDR+4]]))
CHECK-NEXT:       DW_AT_name ("{{.*}}asm_foo.s")

# Check .debug_aranges was updated for asm module
CHECK: .debug_aranges contents:
CHECK-NEXT:   Address Range Header: length = 0x0000002c, format = DWARF32, version = 0x0002, cu_offset = 0x00000000, addr_size = 0x08, seg_size = 0x00
CHECK-NEXT:   [0x0000000000[[#ADDR]], 0x0000000000[[#ADDR+4]])

# Check line number info was updated
CHECK:       0x0000000000[[#ADDR]]   13      0      0   0             0       0  is_stmt
CHECK-NEXT:  0x0000000000[[#ADDR+4]] 13      0      0   0             0       0  is_stmt end_sequence
