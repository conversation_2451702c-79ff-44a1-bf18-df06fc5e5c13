// This test checks dynamic relocations support for aarch64.

RUN: yaml2obj %p/Inputs/rels-so.yaml &> %t.so
RUN: yaml2obj %p/Inputs/rels-exe.yaml &> %t.exe
RUN: llvm-bolt %t.so -o %t.bolt.so --use-old-text=0 --lite=0
RUN: llvm-bolt %t.exe -o %t.bolt.exe --use-old-text=0 --lite=0

// Check relocations in library:

RUN: llvm-readelf -rW %t.bolt.so | FileCheck %s -check-prefix=CHECKLIB

CHECKLIB: {{.*}} R_AARCH64_GLOB_DAT     {{.*}} a + 0
CHECKLIB: {{.*}} R_AARCH64_TLSDESC      {{.*}} t1 + 0
CHECKLIB: {{.*}} R_AARCH64_ABS64        {{.*}} a + 0

// Check relocations in executable:

RUN: llvm-readelf -rW %t.bolt.exe | FileCheck %s -check-prefix=CHECKEXE

CHECKEXE: {{.*}} R_AARCH64_TLS_TPREL64  {{.*}} t1 + 0
CHECKEXE: {{.*}} R_AARCH64_COPY         {{.*}} a + 0
CHECKEXE: {{.*}} R_AARCH64_JUMP_SLOT    {{.*}} inc + 0

// Check traditional TLS relocations R_AARCH64_TLS_DTPMOD64 and
// R_AARCH64_TLS_DTPREL64 emitted correctly after bolt. Since these
// relocations are obsolete and clang and lld does not support them,
// the initial binary was built with gcc and ld with -mtls-dialect=trad flag.

RUN: yaml2obj %p/Inputs/tls-trad.yaml &> %t.trad.so
RUN: llvm-bolt %t.trad.so -o %t.trad.bolt.so --use-old-text=0 --lite=0
RUN: llvm-readelf -rW %t.trad.so | FileCheck %s -check-prefix=CHECKTRAD

CHECKTRAD: {{.*}} R_AARCH64_TLS_DTPMOD64 {{.*}} t1 + 0
CHECKTRAD: {{.*}} R_AARCH64_TLS_DTPREL64 {{.*}} t1 + 0

// The ld linker emits R_AARCH64_TLSDESC to .rela.plt section, check that
// it is emitted correctly.

RUN: yaml2obj %p/Inputs/tls-ld.yaml &> %t.ld.so
RUN: llvm-bolt %t.ld.so -o %t.ld.bolt.so --use-old-text=0 --lite=0
RUN: llvm-readelf -rW %t.ld.bolt.so | FileCheck %s -check-prefix=CHECKLD

CHECKLD: {{.*}} R_AARCH64_TLSDESC        {{.*}} t1 + 0
