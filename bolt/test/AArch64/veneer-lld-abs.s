## Check that llvm-bolt correctly recognizes veneers/thunks for absolute code
## generated by LLD.

# RUN: llvm-mc -filetype=obj -triple aarch64-unknown-unknown %s -o %t.o
# RUN: %clang %cflags -fno-PIC -no-pie %t.o -o %t.exe -nostdlib \
# RUN:    -fuse-ld=lld -Wl,-q
# RUN: llvm-objdump -d %t.exe | FileCheck --check-prefix=CHECK-INPUT %s
# RUN: llvm-objcopy --remove-section .rela.mytext %t.exe
# RUN: llvm-bolt %t.exe -o %t.bolt
# RUN: llvm-objdump -d -j .text  %t.bolt | \
# RUN:   FileCheck --check-prefix=CHECK-OUTPUT %s

## Occasionally, we see the linker not generating $d symbols for long veneers
## causing BOLT to fail veneer elimination.
# RUN: llvm-objcopy --remove-symbol-prefix=\$d %t.exe %t.no-marker.exe
# RUN: llvm-bolt %t.no-marker.exe -o %t.no-marker.bolt \
# RUN:   2>&1 | FileCheck %s --check-prefix=CHECK-BOLT
# RUN: llvm-objdump -d -j .text  %t.no-marker.bolt | \
# RUN:   FileCheck --check-prefix=CHECK-OUTPUT %s

# CHECK-BOLT-NOT: BOLT-WARNING: unable to disassemble instruction
# CHECK-BOLT: BOLT-WARNING: missing data marker detected in veneer __AArch64AbsLongThunk_far_function

.text
.balign 4
.global far_function
.type far_function, %function
far_function:
  ret
.size far_function, .-far_function

.global near_function
.type near_function, %function
near_function:
  ret
.size near_function, .-near_function

## Force relocations against .text.
.reloc 0, R_AARCH64_NONE

.section ".mytext", "ax"
.balign 4

## This version of a thunk is always generated by LLD for function calls
## spanning more than 256MB.
.global __AArch64AbsLongThunk_far_function
.type __AArch64AbsLongThunk_far_function, %function
__AArch64AbsLongThunk_far_function:
  ldr x16, .L1
  br x16
# CHECK-INPUT-LABEL: <__AArch64AbsLongThunk_far_function>:
# CHECK-INPUT-NEXT:    ldr
# CHECK-INPUT-NEXT:    br
.L1:
  .quad far_function
.size __AArch64AbsLongThunk_far_function, .-__AArch64AbsLongThunk_far_function

## If a callee is closer than 256MB away, LLD may generate a thunk with a direct
## jump to the callee. Note, that the name might still include "AbsLong".
.global __AArch64AbsLongThunk_near_function
.type __AArch64AbsLongThunk_near_function, %function
__AArch64AbsLongThunk_near_function:
  b near_function
# CHECK-INPUT-LABEL: <__AArch64AbsLongThunk_near_function>:
# CHECK-INPUT-NEXT:    b {{.*}} <near_function>
.size __AArch64AbsLongThunk_near_function, .-__AArch64AbsLongThunk_near_function

## Check that thunks were removed from .text, and _start calls functions
## directly.

# CHECK-OUTPUT-NOT: __AArch64AbsLongThunk_{{.*}}

.global _start
.type _start, %function
_start:
# CHECK-INPUT-LABEL:  <_start>:
# CHECK-OUTPUT-LABEL: <_start>:
  bl __AArch64AbsLongThunk_far_function
  bl __AArch64AbsLongThunk_near_function
# CHECK-INPUT-NEXT:     bl {{.*}} <__AArch64AbsLongThunk_far_function>
# CHECK-INPUT-NEXT:     bl {{.*}} <__AArch64AbsLongThunk_near_function>
# CHECK-OUTPUT-NEXT:    bl {{.*}} <far_function>
# CHECK-OUTPUT-NEXT:    bl {{.*}} <near_function>
  ret
.size _start, .-_start
