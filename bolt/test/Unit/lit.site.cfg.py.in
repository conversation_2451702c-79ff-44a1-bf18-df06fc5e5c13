@LIT_SITE_CFG_IN_HEADER@

config.llvm_src_root = "@LLVM_SOURCE_DIR@"
config.llvm_obj_root = "@LLVM_BINARY_DIR@"
config.llvm_tools_dir = lit_config.substitute("@LLVM_TOOLS_DIR@")
config.llvm_libs_dir = lit_config.substitute("@LLVM_LIBS_DIR@")
config.llvm_build_mode = lit_config.substitute("@LLVM_BUILD_MODE@")
config.lit_tools_dir = "@LLVM_LIT_TOOLS_DIR@"
config.bolt_obj_root = "@BOLT_BINARY_DIR@"
config.bolt_src_root = "@BOLT_SOURCE_DIR@"
config.target_triple = "@LLVM_TARGET_TRIPLE@"

import lit.llvm
lit.llvm.initialize(lit_config, config)

# Let the main config do the real work.
lit_config.load_config(config, "@BOLT_SOURCE_DIR@/test/Unit/lit.cfg.py")
