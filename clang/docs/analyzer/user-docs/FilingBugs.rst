Filing Bugs and Feature Requests
================================

We encourage users to file bug reports for any problems that they encounter.
We also welcome feature requests. When filing a bug report, please do the
following:

- Include the checker build (for prebuilt Mac OS X binaries) or the git hash.

- Provide a self-contained, reduced test case that exhibits the issue you are
  experiencing.

- Test cases don't tell us everything. Please briefly describe the problem you
  are seeing, including what you thought should have been the expected behavior
  and why.

Please `file bugs and feature requests <https://llvm.org/docs/HowToSubmitABug.html>`_
in `LLVM's issue tracker <https://github.com/llvm/llvm-project/issues>`_ and label the report with the ``clang:static analyzer`` label.
