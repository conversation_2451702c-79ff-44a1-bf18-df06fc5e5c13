========================
Configuring the Analyzer
========================

The clang static analyzer supports two kinds of options:

1. Global **analyzer options** influence the behavior of the analyzer engine.
   They are documented on this page, in the section :ref:`List of analyzer
   options<list-of-analyzer-options>`.
2. The **checker options** belong to individual checkers (e.g.
   ``core.BitwiseShift:Pedantic`` and ``unix.Stream:Pedantic`` are completely
   separate options) and customize the behavior of that particular checker.
   These are documented within the documentation of each individual checker at
   :doc:`../checkers`.

Assigning values to options
===========================

With the compiler frontend
--------------------------

All options can be configured by using the ``-analyzer-config`` flag of ``clang
-cc1`` (the so-called *compiler frontend* part of clang). The values of the
options are specified with the syntax ``-analyzer-config
OPT=VAL,OPT2=VAL2,...`` which supports specifying multiple options, but
separate flags like ``-analyzer-config OPT=VAL -analyzer-config OPT2=VAL2`` are
also accepted (with equivalent behavior). Analyzer options and checker options
can be freely intermixed here because it's easy to recognize that checker
option names are always prefixed with ``some.groups.NameOfChecker:``.

.. warning::
   This is an internal interface, one should prefer `clang --analyze ...` for
   regular use. Clang does not intend to preserve backwards compatibility or
   announce breaking changes within the flags accepted by ``clang -cc1``
   (but ``-analyzer-config`` survived many years without major changes).

With the clang driver
---------------------

In a conventional workflow ``clang -cc1`` (which is a low-level internal
interface) is invoked indirectly by the clang *driver* (i.e. plain ``clang``
without the ``-cc1`` flag), which acts as an "even more frontend" wrapper layer
around the ``clang -cc1`` *compiler frontend*. In this situation **each**
command line argument intended for the *compiler frontend* must be prefixed
with ``-Xclang``.

For example the following command analyzes ``foo.c`` in :ref:`shallow mode
<analyzer-option-mode>` with :ref:`loop unrolling
<analyzer-option-unroll-loops>`:

::

  clang --analyze -Xclang -analyzer-config -Xclang mode=shallow,unroll-loops=true foo.c

When this is executed, the *driver* will compose and execute the following
``clang -cc1`` command (which can be inspected by passing the ``-v`` flag to
the *driver*):

::

  clang -cc1 -analyze [...] -analyzer-config mode=shallow,unroll-loops=true foo.c

Here ``[...]`` stands for dozens of low-level flags which ensure that ``clang
-cc1`` does the right thing (e.g. ``-fcolor-diagnostics`` when it's suitable;
``-analyzer-checker`` flags to enable the default set of checkers). Also
note the distinction that the ``clang`` *driver* requires ``--analyze`` (double
dashes) while the ``clang -cc1`` *compiler frontend* requires ``-analyze``
(single dash).

.. note::
   The flag ``-Xanalyzer`` is equivalent to ``-Xclang`` in these situations
   (but doesn't forward other options of the clang frontend).

With CodeChecker
----------------

If the analysis is performed through :ref:`CodeChecker
<command-line-usage-CodeChecker>` (which e.g. supports the analysis of a whole
project instead of a single file) then it will act as another indirection
layer. CodeChecker provides separate command-line flags called
``--analyzer-config`` (for analyzer options) and ``--checker-config`` (for
checker options):

::

  CodeChecker analyze -o outdir --checker-config clangsa:unix.Stream:Pedantic=true  \
          --analyzer-config clangsa:mode=shallow clangsa:unroll-loops=true          \
          -- compile_commands.json

These CodeChecker flags may be followed by multiple ``OPT=VAL`` pairs as
separate arguments (and this is why the example needs to use ``--`` before
``compile_commands.json``). The option names are all prefixed with ``clangsa:``
to ensure that they are passed to the clang static analyzer (and not other
analyzer tools that are also supported by CodeChecker).

.. _list-of-analyzer-options:

List of analyzer options
========================

.. warning::
   These options are primarily intended for development purposes and
   non-default values are usually unsupported. Changing their values may
   drastically alter the behavior of the analyzer, and may even result in
   instabilities or crashes! Crash reports are welcome and depending on the
   severity they may be fixed.

..
   The contents of this section are automatically generated by the script
   clang/docs/tools/generate_analyzer_options_docs.py from the header file
   AnalyzerOptions.def to ensure that the RST/web documentation is synchronized
   with the command line help options.

.. OPTIONS_LIST_PLACEHOLDER
