Obtaining the Static Analyzer
=============================

This page describes how to download and install the analyzer. Once the analyzer is installed, follow the :doc:`CommandLineUsage` on using the command line to get started analyzing your code.

.. contents::
   :local:


Building the Analyzer from Source
---------------------------------

Currently there are no officially supported binary distributions for the static analyzer.
You must build Clang and LLVM manually.
To do so, please follow the instructions for `building Clang from source code <https://clang.llvm.org/get_started.html#build>`_.

Once the Clang is built, you need to add the location of the ``clang`` binary and the locations of the command line utilities (`<PERSON><PERSON>hecker` or ``scan-build`` and ``scan-view``) to you PATH for :doc:`CommandLineUsage`.

[Legacy] Packaged Builds (Mac OS X)
-----------------------------------

Semi-regular pre-built binaries of the analyzer used to be available on Mac OS X. These were built to run on OS X 10.7 and later.

For older builds for MacOS visit https://clang-analyzer.llvm.org/release_notes.html.

Packaged builds for other platforms may eventually be provided, but we need volunteers who are willing to help provide such regular builds. If you wish to help contribute regular builds of the analyzer on other platforms, please get in touch via `LLVM Discourse <https://discourse.llvm.org/>`_.

[Legacy] Using Packaged Builds
------------------------------

To use the legacy package builds, simply unpack it anywhere. If the build archive has the name **``checker-XXX.tar.bz2``** then the archive will expand to a directory called **``checker-XXX``**. You do not need to place this directory or the contents of this directory in any special place. Uninstalling the analyzer is as simple as deleting this directory.

Most of the files in the **``checker-XXX``** directory will be supporting files for the analyzer that you can simply ignore. Most users will only care about two files, which are located at the top of the **``checker-XXX``** directory:

* **scan-build**: ``scan-build`` is the high-level command line utility for running the analyzer
* **scan-view**: ``scan-view`` a companion command line utility to ``scan-build``, ``scan-view`` is used to view analysis results generated by ``scan-build``. There is an option that one can pass to ``scan-build`` to cause ``scan-view`` to run as soon as it the analysis of a build completes

