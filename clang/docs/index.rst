.. Clang documentation master file, created by
   sphinx-quickstart on Sun Dec  9 20:01:55 2012.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

.. title:: Welcome to Clang's documentation!

.. toctree::
   :maxdepth: 1

   ReleaseNotes

Using Clang as a Compiler
=========================

.. toctree::
   :maxdepth: 1

   UsersManual
   Toolchain
   LanguageExtensions
   ClangCommandLineReference
   AttributeReference
   DiagnosticsReference
   WarningSuppressionMappings
   CrossCompilation
   ClangStaticAnalyzer
   ThreadSafetyAnalysis
   SafeBuffers
   DataFlowAnalysisIntro
   FunctionEffectAnalysis
   AddressSanitizer
   ThreadSanitizer
   MemorySanitizer
   UndefinedBehaviorSanitizer
   DataFlowSanitizer
   LeakSanitizer
   TypeSanitizer
   RealtimeSanitizer
   SanitizerCoverage
   SanitizerStats
   SanitizerSpecialCaseList
   BoundsSafety
   BoundsSafetyAdoptionGuide
   BoundsSafetyImplPlans
   ControlFlowIntegrity
   LTOVisibility
   SafeStack
   ShadowCallStack
   SourceBasedCodeCoverage
   StandardCPlusPlusModules
   Modules
   MSVCCompatibility
   MisExpect
   OpenCLSupport
   OpenMPSupport
   SYCLSupport
   HIPSupport
   HLSL/HLSLDocs
   ThinLTO
   APINotes
   DebuggingCoroutines
   AMDGPUSupport
   CommandGuide/index
   FAQ

Using Clang as a Library
========================

.. toctree::
   :maxdepth: 1

   Tooling
   ExternalClangExamples
   IntroductionToTheClangAST
   LibTooling
   LibClang
   LibFormat
   ClangPlugins
   RAVFrontendAction
   LibASTMatchersTutorial
   LibASTMatchers
   ClangTransformerTutorial
   LibASTImporter
   HowToSetupToolingForLLVM
   JSONCompilationDatabase
   RefactoringEngine

Using Clang Tools
=================

.. toctree::
   :maxdepth: 1

   ClangTools
   ClangCheck
   ClangFormat
   ClangFormatStyleOptions
   ClangLinkerWrapper
   ClangNVLinkWrapper
   ClangOffloadBundler
   ClangOffloadPackager
   ClangRepl
   ClangSYCLLinker

Design Documents
================

.. toctree::
   :maxdepth: 1

   Maintainers
   InternalsManual
   DriverInternals
   Multilib
   OffloadingDesign
   PCHInternals
   ItaniumMangleAbiTags
   HardwareAssistedAddressSanitizerDesign.rst
   ConstantInterpreter


Indices and tables
==================

* :ref:`genindex`
* :ref:`search`

