=====================
Clang Static Analyzer
=====================

The Clang Static Analyzer is a source code analysis tool that finds bugs in C, C++, and Objective-C programs.
It implements *path-sensitive*, *inter-procedural analysis* based on *symbolic execution* technique.

This is the Static Analyzer documentation page.

See the `Official Tool Page <https://clang-analyzer.llvm.org/>`_.

.. toctree::
   :caption: Table of Contents
   :numbered:
   :maxdepth: 2

   analyzer/checkers
   analyzer/user-docs
   analyzer/developer-docs

