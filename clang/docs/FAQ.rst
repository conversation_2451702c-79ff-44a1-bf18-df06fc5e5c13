================================
Frequently Asked Questions (FAQ)
================================

.. contents::
   :local:

Driver
======

I run ``clang -cc1 ...`` and get weird errors about missing headers
-------------------------------------------------------------------

Given this source file:

.. code-block:: c

  #include <stdio.h>

  int main() {
    printf("Hello world\n");
  }


If you run:

.. code-block:: console

  $ clang -cc1 hello.c
  hello.c:1:10: fatal error: 'stdio.h' file not found
  #include <stdio.h>
           ^
  1 error generated.

``clang -cc1`` is the frontend, ``clang`` is the :doc:`driver
<DriverInternals>`.  The driver invokes the frontend with options appropriate
for your system.  To see these options, run:

.. code-block:: console

  $ clang -### -c hello.c

Some clang command line options are driver-only options, some are frontend-only
options.  Frontend-only options are intended to be used only by clang developers.
Users should not run ``clang -cc1`` directly, because ``-cc1`` options are not
guaranteed to be stable.

If you want to use a frontend-only option ("a ``-cc1`` option"), for example
``-ast-dump``, then you need to take the ``clang -cc1`` line generated by the
driver and add the option you need.  Alternatively, you can run
``clang -Xclang <option> ...`` to force the driver pass ``<option>`` to
``clang -cc1``.

I get errors about some headers being missing (``stddef.h``, ``stdarg.h``)
--------------------------------------------------------------------------

Some header files (``stddef.h``, ``stdarg.h``, and others) are shipped with
Clang --- these are called builtin includes.  Clang searches for them in a
directory relative to the location of the ``clang`` binary.  If you moved the
``clang`` binary, you need to move the builtin headers, too.

More information can be found in the :ref:`libtooling_builtin_includes`
section.

