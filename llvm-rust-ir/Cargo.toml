[package]
name = "llvm-rust-ir"
version = "0.1.0"
edition = "2021"
authors = ["LLVM Rust IR Team"]
description = "Pure Rust implementation of LLVM IR data structures"
license = "Apache-2.0 WITH LLVM-exception"

[features]
default = ["parallel"]
parallel = ["rayon"]
simd = []
codegen = ["uuid"]

[dependencies]
# Core dependencies for memory-safe IR
rayon = { version = "1.7", optional = true }
indexmap = "2.0"
smallvec = "1.11"
parking_lot = "0.12"

# For SIMD optimizations
wide = { version = "0.7", optional = true }

# For SelectionDAG and code generation
uuid = { version = "1.0", features = ["v4"], optional = true }

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.2"
tracing = "0.1"
tracing-subscriber = "0.3"

[lib]
name = "llvm_rust_ir"
path = "src/lib.rs"

# Ensure memory safety
[profile.dev]
overflow-checks = true

[profile.release]
overflow-checks = true
lto = true
codegen-units = 1
panic = "abort"

[[bench]]
name = "ir_benchmarks"
harness = false
