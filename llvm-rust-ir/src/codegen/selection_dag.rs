//! # SelectionDAG - Pure Rust Implementation
//!
//! This module provides a memory-safe, parallel-by-default implementation of
//! LLVM's SelectionDAG, replacing the C++ implementation with zero unsafe code.
//!
//! ## Key Improvements over C++ LLVM
//!
//! - **Memory Safety**: No raw pointers, automatic memory management
//! - **Thread Safety**: Built-in support for parallel operations
//! - **Performance**: Enum dispatch instead of virtual function calls
//! - **Correctness**: Compile-time guarantees prevent many classes of bugs

use crate::{Value, ValueId, Type, Context};
use super::{CodeGenError, CodeGenResult};
use std::collections::HashMap;
use std::sync::Arc;

#[cfg(feature = "uuid")]
use uuid::Uuid;

/// Unique identifier for SelectionDAG nodes
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash)]
pub struct SDNodeId(pub u64);

impl SDNodeId {
    /// Create a new unique node ID
    #[cfg(feature = "uuid")]
    pub fn new() -> Self {
        SDNodeId(Uuid::new_v4().as_u128() as u64)
    }
    
    /// Create a new node ID from a counter (fallback when uuid is not available)
    #[cfg(not(feature = "uuid"))]
    pub fn new() -> Self {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(0);
        SDNodeId(COUNTER.fetch_add(1, Ordering::SeqCst))
    }
}

/// SelectionDAG value - represents a value produced by a DAG node
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct SDValue {
    /// ID of the node that produces this value
    pub node_id: SDNodeId,
    /// Which result of the node (nodes can produce multiple values)
    pub result_number: u32,
}

impl SDValue {
    /// Create a new SDValue
    pub fn new(node_id: SDNodeId, result_number: u32) -> Self {
        Self { node_id, result_number }
    }
    
    /// Get the node ID
    pub fn get_node_id(&self) -> SDNodeId {
        self.node_id
    }
    
    /// Get the result number
    pub fn get_result_number(&self) -> u32 {
        self.result_number
    }
}

/// SelectionDAG node types - memory-safe enum instead of C++ class hierarchy
#[derive(Debug, Clone)]
pub enum SDNode {
    /// Entry node - represents the start of the DAG
    Entry {
        id: SDNodeId,
    },
    
    /// Arithmetic operations
    Add {
        id: SDNodeId,
        lhs: SDValue,
        rhs: SDValue,
        flags: ArithmeticFlags,
        result_type: Arc<Type>,
    },
    
    Sub {
        id: SDNodeId,
        lhs: SDValue,
        rhs: SDValue,
        flags: ArithmeticFlags,
        result_type: Arc<Type>,
    },
    
    Mul {
        id: SDNodeId,
        lhs: SDValue,
        rhs: SDValue,
        flags: ArithmeticFlags,
        result_type: Arc<Type>,
    },
    
    /// Memory operations
    Load {
        id: SDNodeId,
        ptr: SDValue,
        chain: SDValue,
        memory_vt: Arc<Type>,
        alignment: u32,
        volatile: bool,
    },
    
    Store {
        id: SDNodeId,
        value: SDValue,
        ptr: SDValue,
        chain: SDValue,
        alignment: u32,
        volatile: bool,
    },
    
    /// Control flow operations
    Branch {
        id: SDNodeId,
        condition: Option<SDValue>,
        targets: Vec<String>, // Basic block labels
    },
    
    Return {
        id: SDNodeId,
        value: Option<SDValue>,
        chain: SDValue,
    },
    
    /// Constants
    Constant {
        id: SDNodeId,
        value: ConstantValue,
        result_type: Arc<Type>,
    },
    
    /// Target-specific nodes
    TargetNode {
        id: SDNodeId,
        opcode: u32,
        operands: Vec<SDValue>,
        result_types: Vec<Arc<Type>>,
    },
}

/// Arithmetic operation flags
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct ArithmeticFlags {
    /// No signed wrap
    pub nsw: bool,
    /// No unsigned wrap
    pub nuw: bool,
    /// Exact division
    pub exact: bool,
}

impl Default for ArithmeticFlags {
    fn default() -> Self {
        Self {
            nsw: false,
            nuw: false,
            exact: false,
        }
    }
}

/// Constant values in the DAG
#[derive(Debug, Clone)]
pub enum ConstantValue {
    /// Integer constant
    Integer(i64),
    /// Floating-point constant
    Float(f64),
    /// Null pointer constant
    Null,
    /// Undefined value
    Undef,
}

impl SDNode {
    /// Get the ID of this node
    pub fn get_id(&self) -> SDNodeId {
        match self {
            SDNode::Entry { id } => *id,
            SDNode::Add { id, .. } => *id,
            SDNode::Sub { id, .. } => *id,
            SDNode::Mul { id, .. } => *id,
            SDNode::Load { id, .. } => *id,
            SDNode::Store { id, .. } => *id,
            SDNode::Branch { id, .. } => *id,
            SDNode::Return { id, .. } => *id,
            SDNode::Constant { id, .. } => *id,
            SDNode::TargetNode { id, .. } => *id,
        }
    }
    
    /// Get the operands of this node
    pub fn get_operands(&self) -> Vec<SDValue> {
        match self {
            SDNode::Entry { .. } => vec![],
            SDNode::Add { lhs, rhs, .. } => vec![*lhs, *rhs],
            SDNode::Sub { lhs, rhs, .. } => vec![*lhs, *rhs],
            SDNode::Mul { lhs, rhs, .. } => vec![*lhs, *rhs],
            SDNode::Load { ptr, chain, .. } => vec![*ptr, *chain],
            SDNode::Store { value, ptr, chain, .. } => vec![*value, *ptr, *chain],
            SDNode::Branch { condition, .. } => {
                condition.map(|c| vec![c]).unwrap_or_else(Vec::new)
            }
            SDNode::Return { value, chain, .. } => {
                let mut ops = vec![*chain];
                if let Some(val) = value {
                    ops.push(*val);
                }
                ops
            }
            SDNode::Constant { .. } => vec![],
            SDNode::TargetNode { operands, .. } => operands.clone(),
        }
    }
    
    /// Get the result types of this node
    pub fn get_result_types(&self) -> Vec<Arc<Type>> {
        match self {
            SDNode::Entry { .. } => vec![], // Entry node has no results
            SDNode::Add { result_type, .. } => vec![result_type.clone()],
            SDNode::Sub { result_type, .. } => vec![result_type.clone()],
            SDNode::Mul { result_type, .. } => vec![result_type.clone()],
            SDNode::Load { memory_vt, .. } => vec![memory_vt.clone()],
            SDNode::Store { .. } => vec![], // Store returns void
            SDNode::Branch { .. } => vec![], // Branch returns void
            SDNode::Return { .. } => vec![], // Return returns void
            SDNode::Constant { result_type, .. } => vec![result_type.clone()],
            SDNode::TargetNode { result_types, .. } => result_types.clone(),
        }
    }
    
    /// Check if this node has side effects
    pub fn has_side_effects(&self) -> bool {
        match self {
            SDNode::Entry { .. } => false,
            SDNode::Add { .. } => false,
            SDNode::Sub { .. } => false,
            SDNode::Mul { .. } => false,
            SDNode::Load { volatile, .. } => *volatile,
            SDNode::Store { .. } => true,
            SDNode::Branch { .. } => true,
            SDNode::Return { .. } => true,
            SDNode::Constant { .. } => false,
            SDNode::TargetNode { .. } => true, // Conservative assumption
        }
    }
}

/// Pure Rust SelectionDAG - replaces LLVM's C++ implementation
pub struct SelectionDAG {
    /// All nodes in the DAG
    nodes: HashMap<SDNodeId, SDNode>,
    /// Root node of the DAG
    root: Option<SDNodeId>,
    /// Entry node of the DAG
    entry_node: SDNodeId,
    /// Context for type management
    context: Arc<Context>,
    /// Next available node ID
    next_id_counter: u64,
}

impl SelectionDAG {
    /// Create a new SelectionDAG
    pub fn new(context: Arc<Context>) -> Self {
        let entry_id = SDNodeId::new();
        let entry_node = SDNode::Entry { id: entry_id };
        
        let mut nodes = HashMap::new();
        nodes.insert(entry_id, entry_node);
        
        Self {
            nodes,
            root: None,
            entry_node: entry_id,
            context,
            next_id_counter: 0,
        }
    }
    
    /// Get a node by ID
    pub fn get_node(&self, id: SDNodeId) -> Option<&SDNode> {
        self.nodes.get(&id)
    }
    
    /// Get a mutable reference to a node by ID
    pub fn get_node_mut(&mut self, id: SDNodeId) -> Option<&mut SDNode> {
        self.nodes.get_mut(&id)
    }
    
    /// Check if a node ID is valid
    pub fn is_valid_node(&self, id: SDNodeId) -> bool {
        self.nodes.contains_key(&id)
    }
    
    /// Get all node IDs
    pub fn all_nodes(&self) -> impl Iterator<Item = SDNodeId> + '_ {
        self.nodes.keys().copied()
    }
    
    /// Get the entry node
    pub fn get_entry_node(&self) -> SDNodeId {
        self.entry_node
    }
    
    /// Get the root node
    pub fn get_root(&self) -> Option<SDNodeId> {
        self.root
    }
    
    /// Set the root node
    pub fn set_root(&mut self, root: SDNodeId) -> CodeGenResult<()> {
        if !self.is_valid_node(root) {
            return Err(CodeGenError::InvalidDAGOperation(
                format!("Cannot set invalid node {} as root", root.0)
            ));
        }
        self.root = Some(root);
        Ok(())
    }

    /// Create an ADD node
    pub fn create_add(&mut self, lhs: SDValue, rhs: SDValue, result_type: Arc<Type>) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Add {
            id,
            lhs,
            rhs,
            flags: ArithmeticFlags::default(),
            result_type,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Create a SUB node
    pub fn create_sub(&mut self, lhs: SDValue, rhs: SDValue, result_type: Arc<Type>) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Sub {
            id,
            lhs,
            rhs,
            flags: ArithmeticFlags::default(),
            result_type,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Create a MUL node
    pub fn create_mul(&mut self, lhs: SDValue, rhs: SDValue, result_type: Arc<Type>) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Mul {
            id,
            lhs,
            rhs,
            flags: ArithmeticFlags::default(),
            result_type,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Create a LOAD node
    pub fn create_load(&mut self, ptr: SDValue, chain: SDValue, memory_vt: Arc<Type>, alignment: u32) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Load {
            id,
            ptr,
            chain,
            memory_vt,
            alignment,
            volatile: false,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Create a STORE node
    pub fn create_store(&mut self, value: SDValue, ptr: SDValue, chain: SDValue, alignment: u32) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Store {
            id,
            value,
            ptr,
            chain,
            alignment,
            volatile: false,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Create a constant node
    pub fn create_constant(&mut self, value: ConstantValue, result_type: Arc<Type>) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Constant {
            id,
            value,
            result_type,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Create a RETURN node
    pub fn create_return(&mut self, value: Option<SDValue>, chain: SDValue) -> CodeGenResult<SDValue> {
        let id = SDNodeId::new();
        let node = SDNode::Return {
            id,
            value,
            chain,
        };

        self.nodes.insert(id, node);
        Ok(SDValue::new(id, 0))
    }

    /// Remove a node from the DAG
    pub fn remove_node(&mut self, id: SDNodeId) -> CodeGenResult<()> {
        if !self.is_valid_node(id) {
            return Err(CodeGenError::InvalidDAGOperation(
                format!("Cannot remove invalid node {}", id.0)
            ));
        }

        // Check if this is the root node
        if self.root == Some(id) {
            self.root = None;
        }

        self.nodes.remove(&id);
        Ok(())
    }

    /// Get the number of nodes in the DAG
    pub fn node_count(&self) -> usize {
        self.nodes.len()
    }
}
