//! # Machine Instructions - Pure Rust Implementation
//!
//! This module provides memory-safe machine instruction representation,
//! replacing LLVM's C++ MachineInstr with Rust enums and structs.

use super::{Code<PERSON>enError, CodeGenResult};
use std::fmt;

/// Target-specific opcode
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct TargetOpcode(pub u32);

impl TargetOpcode {
    /// Create a new target opcode
    pub fn new(opcode: u32) -> Self {
        Self(opcode)
    }
    
    /// Get the opcode value
    pub fn value(&self) -> u32 {
        self.0
    }
}

/// Machine instruction - memory-safe replacement for LLVM's MachineInstr
#[derive(Debug, Clone)]
pub struct MachineInstr {
    /// Target-specific opcode
    opcode: TargetOpcode,
    /// Operands of the instruction
    operands: Vec<MachineOperand>,
    /// Flags for the instruction
    flags: InstructionFlags,
    /// Debug information
    debug_loc: Option<DebugLocation>,
}

/// Machine operand types
#[derive(Debug, <PERSON>lone)]
pub enum MachineOperand {
    /// Register operand
    Register {
        /// Register number
        reg: u32,
        /// Is this a definition?
        is_def: bool,
        /// Is this operand killed?
        is_kill: bool,
        /// Is this operand dead?
        is_dead: bool,
    },
    
    /// Immediate operand
    Immediate {
        /// Immediate value
        value: i64,
    },
    
    /// Memory operand
    Memory {
        /// Base register
        base: u32,
        /// Index register (optional)
        index: Option<u32>,
        /// Scale factor
        scale: u32,
        /// Displacement
        displacement: i64,
        /// Segment register (x86-specific)
        segment: Option<u32>,
    },
    
    /// Basic block operand
    BasicBlock {
        /// Basic block label
        label: String,
    },
    
    /// Global address operand
    GlobalAddress {
        /// Symbol name
        symbol: String,
        /// Offset from symbol
        offset: i64,
    },
    
    /// Frame index operand
    FrameIndex {
        /// Frame index
        index: i32,
    },
    
    /// Constant pool operand
    ConstantPool {
        /// Constant pool index
        index: u32,
        /// Offset
        offset: i64,
    },
}

/// Instruction flags
#[derive(Debug, Clone, Copy, Default)]
pub struct InstructionFlags {
    /// Is this instruction a call?
    pub is_call: bool,
    /// Is this instruction a return?
    pub is_return: bool,
    /// Is this instruction a branch?
    pub is_branch: bool,
    /// Is this instruction a terminator?
    pub is_terminator: bool,
    /// Does this instruction have side effects?
    pub has_side_effects: bool,
    /// Is this instruction commutative?
    pub is_commutative: bool,
}

/// Debug location information
#[derive(Debug, Clone)]
pub struct DebugLocation {
    /// Line number
    pub line: u32,
    /// Column number
    pub column: u32,
    /// File name
    pub filename: String,
}

impl MachineInstr {
    /// Create a new machine instruction
    pub fn new(opcode: TargetOpcode) -> Self {
        Self {
            opcode,
            operands: Vec::new(),
            flags: InstructionFlags::default(),
            debug_loc: None,
        }
    }
    
    /// Create a machine instruction with operands
    pub fn with_operands(opcode: TargetOpcode, operands: Vec<MachineOperand>) -> Self {
        Self {
            opcode,
            operands,
            flags: InstructionFlags::default(),
            debug_loc: None,
        }
    }
    
    /// Get the opcode
    pub fn get_opcode(&self) -> TargetOpcode {
        self.opcode
    }
    
    /// Get the operands
    pub fn get_operands(&self) -> &[MachineOperand] {
        &self.operands
    }
    
    /// Get mutable operands
    pub fn get_operands_mut(&mut self) -> &mut Vec<MachineOperand> {
        &mut self.operands
    }
    
    /// Add an operand
    pub fn add_operand(&mut self, operand: MachineOperand) {
        self.operands.push(operand);
    }
    
    /// Get the flags
    pub fn get_flags(&self) -> InstructionFlags {
        self.flags
    }
    
    /// Set the flags
    pub fn set_flags(&mut self, flags: InstructionFlags) {
        self.flags = flags;
    }
    
    /// Set debug location
    pub fn set_debug_location(&mut self, debug_loc: DebugLocation) {
        self.debug_loc = Some(debug_loc);
    }
    
    /// Get debug location
    pub fn get_debug_location(&self) -> Option<&DebugLocation> {
        self.debug_loc.as_ref()
    }
    
    /// Check if this instruction defines a register
    pub fn defines_register(&self, reg: u32) -> bool {
        self.operands.iter().any(|op| {
            matches!(op, MachineOperand::Register { reg: r, is_def: true, .. } if *r == reg)
        })
    }
    
    /// Check if this instruction uses a register
    pub fn uses_register(&self, reg: u32) -> bool {
        self.operands.iter().any(|op| {
            matches!(op, MachineOperand::Register { reg: r, is_def: false, .. } if *r == reg)
        })
    }
    
    /// Get all defined registers
    pub fn get_defined_registers(&self) -> Vec<u32> {
        self.operands.iter()
            .filter_map(|op| {
                if let MachineOperand::Register { reg, is_def: true, .. } = op {
                    Some(*reg)
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Get all used registers
    pub fn get_used_registers(&self) -> Vec<u32> {
        self.operands.iter()
            .filter_map(|op| {
                if let MachineOperand::Register { reg, is_def: false, .. } = op {
                    Some(*reg)
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Check if this instruction is a terminator
    pub fn is_terminator(&self) -> bool {
        self.flags.is_terminator || self.flags.is_return || self.flags.is_branch
    }
    
    /// Check if this instruction has side effects
    pub fn has_side_effects(&self) -> bool {
        self.flags.has_side_effects || self.flags.is_call || self.is_terminator()
    }
}

impl fmt::Display for MachineInstr {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "opcode_{}", self.opcode.0)?;
        
        for (i, operand) in self.operands.iter().enumerate() {
            if i == 0 {
                write!(f, " ")?;
            } else {
                write!(f, ", ")?;
            }
            write!(f, "{}", operand)?;
        }
        
        Ok(())
    }
}

impl fmt::Display for MachineOperand {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            MachineOperand::Register { reg, is_def, is_kill, is_dead } => {
                if *is_def {
                    write!(f, "%{}", reg)?;
                    if *is_dead {
                        write!(f, "<dead>")?;
                    }
                } else {
                    write!(f, "%{}", reg)?;
                    if *is_kill {
                        write!(f, "<kill>")?;
                    }
                }
                Ok(())
            }
            MachineOperand::Immediate { value } => write!(f, "#{}", value),
            MachineOperand::Memory { base, index, scale, displacement, .. } => {
                write!(f, "[%{}", base)?;
                if let Some(idx) = index {
                    write!(f, " + %{}", idx)?;
                    if *scale > 1 {
                        write!(f, " * {}", scale)?;
                    }
                }
                if *displacement != 0 {
                    if *displacement > 0 {
                        write!(f, " + {}", displacement)?;
                    } else {
                        write!(f, " - {}", -displacement)?;
                    }
                }
                write!(f, "]")
            }
            MachineOperand::BasicBlock { label } => write!(f, ".{}", label),
            MachineOperand::GlobalAddress { symbol, offset } => {
                if *offset == 0 {
                    write!(f, "@{}", symbol)
                } else {
                    write!(f, "@{} + {}", symbol, offset)
                }
            }
            MachineOperand::FrameIndex { index } => write!(f, "FI#{}", index),
            MachineOperand::ConstantPool { index, offset } => {
                if *offset == 0 {
                    write!(f, "CP#{}", index)
                } else {
                    write!(f, "CP#{} + {}", index, offset)
                }
            }
        }
    }
}

/// Builder for machine instructions
pub struct MachineInstrBuilder {
    instruction: MachineInstr,
}

impl MachineInstrBuilder {
    /// Create a new builder
    pub fn new(opcode: TargetOpcode) -> Self {
        Self {
            instruction: MachineInstr::new(opcode),
        }
    }
    
    /// Add a register operand
    pub fn add_reg(mut self, reg: u32, is_def: bool) -> Self {
        self.instruction.add_operand(MachineOperand::Register {
            reg,
            is_def,
            is_kill: false,
            is_dead: false,
        });
        self
    }
    
    /// Add an immediate operand
    pub fn add_imm(mut self, value: i64) -> Self {
        self.instruction.add_operand(MachineOperand::Immediate { value });
        self
    }
    
    /// Add a memory operand
    pub fn add_mem(mut self, base: u32, displacement: i64) -> Self {
        self.instruction.add_operand(MachineOperand::Memory {
            base,
            index: None,
            scale: 1,
            displacement,
            segment: None,
        });
        self
    }
    
    /// Set instruction flags
    pub fn set_flags(mut self, flags: InstructionFlags) -> Self {
        self.instruction.set_flags(flags);
        self
    }
    
    /// Build the instruction
    pub fn build(self) -> MachineInstr {
        self.instruction
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_machine_instr_creation() {
        let opcode = TargetOpcode::new(42);
        let instr = MachineInstr::new(opcode);
        
        assert_eq!(instr.get_opcode(), opcode);
        assert_eq!(instr.get_operands().len(), 0);
    }
    
    #[test]
    fn test_machine_instr_builder() {
        let opcode = TargetOpcode::new(100);
        let instr = MachineInstrBuilder::new(opcode)
            .add_reg(1, true)  // Define register 1
            .add_reg(2, false) // Use register 2
            .add_imm(42)       // Immediate value 42
            .build();
        
        assert_eq!(instr.get_opcode(), opcode);
        assert_eq!(instr.get_operands().len(), 3);
        assert!(instr.defines_register(1));
        assert!(instr.uses_register(2));
        assert!(!instr.uses_register(1));
    }
    
    #[test]
    fn test_machine_operand_display() {
        let reg_op = MachineOperand::Register {
            reg: 5,
            is_def: true,
            is_kill: false,
            is_dead: false,
        };
        assert_eq!(format!("{}", reg_op), "%5");
        
        let imm_op = MachineOperand::Immediate { value: 42 };
        assert_eq!(format!("{}", imm_op), "#42");
        
        let mem_op = MachineOperand::Memory {
            base: 1,
            index: Some(2),
            scale: 4,
            displacement: 8,
            segment: None,
        };
        assert_eq!(format!("{}", mem_op), "[%1 + %2 * 4 + 8]");
    }
}
