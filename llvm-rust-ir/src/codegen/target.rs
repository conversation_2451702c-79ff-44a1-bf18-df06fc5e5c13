//! # Target Machine Abstraction
//!
//! This module provides target-specific information and abstractions for code generation.

use super::{<PERSON><PERSON>en<PERSON>rror, CodeGenResult};
use std::collections::HashMap;

/// Target machine information
#[derive(Debug, Clone)]
pub struct TargetMachine {
    /// Target triple (e.g., "x86_64-unknown-linux-gnu")
    pub triple: String,
    /// CPU name
    pub cpu: String,
    /// Target features
    pub features: Vec<String>,
    /// Register information
    pub register_info: RegisterInfo,
    /// Instruction information
    pub instruction_info: InstructionInfo,
}

/// Register class information
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct RegisterClass(pub u32);

impl RegisterClass {
    /// Create a new register class
    pub fn new(id: u32) -> Self {
        Self(id)
    }
    
    /// Get the register class ID
    pub fn id(&self) -> u32 {
        self.0
    }
}

/// Register information for the target
#[derive(Debug, Clone)]
pub struct RegisterInfo {
    /// Register classes
    pub register_classes: HashMap<RegisterClass, RegisterClassInfo>,
    /// Physical registers
    pub physical_registers: Hash<PERSON>ap<u32, PhysicalRegister>,
    /// Register aliases
    pub aliases: Hash<PERSON>ap<u32, Vec<u32>>,
}

/// Information about a register class
#[derive(Debug, Clone)]
pub struct RegisterClassInfo {
    /// Name of the register class
    pub name: String,
    /// Size in bits
    pub size_bits: u32,
    /// Alignment in bits
    pub alignment_bits: u32,
    /// Registers in this class
    pub registers: Vec<u32>,
    /// Can this class be allocated?
    pub allocatable: bool,
}

/// Physical register information
#[derive(Debug, Clone)]
pub struct PhysicalRegister {
    /// Register number
    pub number: u32,
    /// Register name
    pub name: String,
    /// Register class
    pub register_class: RegisterClass,
    /// Is this register callee-saved?
    pub callee_saved: bool,
    /// Is this register reserved?
    pub reserved: bool,
}

/// Instruction information for the target
#[derive(Debug, Clone)]
pub struct InstructionInfo {
    /// Instruction descriptions
    pub instructions: HashMap<u32, InstructionDesc>,
    /// Scheduling information
    pub scheduling: SchedulingInfo,
}

/// Description of a target instruction
#[derive(Debug, Clone)]
pub struct InstructionDesc {
    /// Instruction opcode
    pub opcode: u32,
    /// Instruction name
    pub name: String,
    /// Operand information
    pub operands: Vec<OperandInfo>,
    /// Instruction flags
    pub flags: InstructionDescFlags,
    /// Scheduling class
    pub scheduling_class: u32,
}

/// Operand information
#[derive(Debug, Clone)]
pub struct OperandInfo {
    /// Operand type
    pub operand_type: OperandType,
    /// Register class (if applicable)
    pub register_class: Option<RegisterClass>,
    /// Constraints
    pub constraints: Vec<OperandConstraint>,
}

/// Types of operands
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum OperandType {
    /// Register operand
    Register,
    /// Immediate operand
    Immediate,
    /// Memory operand
    Memory,
    /// Basic block operand
    BasicBlock,
}

/// Operand constraints
#[derive(Debug, Clone)]
pub enum OperandConstraint {
    /// Must be the same as another operand
    SameAs(usize),
    /// Must be different from another operand
    DifferentFrom(usize),
    /// Immediate value range
    ImmediateRange(i64, i64),
    /// Custom constraint
    Custom(String),
}

/// Instruction description flags
#[derive(Debug, Clone, Copy, Default)]
pub struct InstructionDescFlags {
    /// Is this a call instruction?
    pub is_call: bool,
    /// Is this a return instruction?
    pub is_return: bool,
    /// Is this a branch instruction?
    pub is_branch: bool,
    /// Is this a terminator instruction?
    pub is_terminator: bool,
    /// Does this instruction have side effects?
    pub has_side_effects: bool,
    /// Is this instruction commutative?
    pub is_commutative: bool,
    /// Can this instruction be rematerialized?
    pub rematerializable: bool,
}

/// Scheduling information
#[derive(Debug, Clone)]
pub struct SchedulingInfo {
    /// Scheduling classes
    pub classes: HashMap<u32, SchedulingClass>,
    /// Resource information
    pub resources: HashMap<u32, ResourceInfo>,
}

/// Scheduling class information
#[derive(Debug, Clone)]
pub struct SchedulingClass {
    /// Class ID
    pub id: u32,
    /// Latency in cycles
    pub latency: u32,
    /// Resources used
    pub resources: Vec<ResourceUsage>,
}

/// Resource usage information
#[derive(Debug, Clone)]
pub struct ResourceUsage {
    /// Resource ID
    pub resource_id: u32,
    /// Number of cycles
    pub cycles: u32,
}

/// Resource information
#[derive(Debug, Clone)]
pub struct ResourceInfo {
    /// Resource ID
    pub id: u32,
    /// Resource name
    pub name: String,
    /// Number of units
    pub units: u32,
}

/// Target information trait
pub trait TargetInfo: Send + Sync {
    /// Get the target machine
    fn get_target_machine(&self) -> &TargetMachine;
    
    /// Get register class for a value type
    fn get_register_class(&self, value_type: &str) -> Option<RegisterClass>;
    
    /// Get the preferred alignment for a type
    fn get_preferred_alignment(&self, value_type: &str) -> u32;
    
    /// Check if an instruction is supported
    fn is_instruction_supported(&self, opcode: u32) -> bool;
    
    /// Get the cost of an instruction
    fn get_instruction_cost(&self, opcode: u32) -> u32;
    
    /// Get calling convention information
    fn get_calling_convention(&self) -> CallingConvention;
}

/// Calling convention information
#[derive(Debug, Clone)]
pub struct CallingConvention {
    /// Argument registers
    pub argument_registers: Vec<u32>,
    /// Return value registers
    pub return_registers: Vec<u32>,
    /// Callee-saved registers
    pub callee_saved_registers: Vec<u32>,
    /// Caller-saved registers
    pub caller_saved_registers: Vec<u32>,
    /// Stack alignment
    pub stack_alignment: u32,
}

impl TargetMachine {
    /// Create a new target machine
    pub fn new(triple: String, cpu: String) -> Self {
        Self {
            triple,
            cpu,
            features: Vec::new(),
            register_info: RegisterInfo {
                register_classes: HashMap::new(),
                physical_registers: HashMap::new(),
                aliases: HashMap::new(),
            },
            instruction_info: InstructionInfo {
                instructions: HashMap::new(),
                scheduling: SchedulingInfo {
                    classes: HashMap::new(),
                    resources: HashMap::new(),
                },
            },
        }
    }
    
    /// Add a target feature
    pub fn add_feature(&mut self, feature: String) {
        self.features.push(feature);
    }
    
    /// Check if a feature is enabled
    pub fn has_feature(&self, feature: &str) -> bool {
        self.features.iter().any(|f| f == feature)
    }
    
    /// Add a register class
    pub fn add_register_class(&mut self, class: RegisterClass, info: RegisterClassInfo) {
        self.register_info.register_classes.insert(class, info);
    }
    
    /// Get register class information
    pub fn get_register_class_info(&self, class: RegisterClass) -> Option<&RegisterClassInfo> {
        self.register_info.register_classes.get(&class)
    }
    
    /// Add a physical register
    pub fn add_physical_register(&mut self, register: PhysicalRegister) {
        self.register_info.physical_registers.insert(register.number, register);
    }
    
    /// Get physical register information
    pub fn get_physical_register(&self, number: u32) -> Option<&PhysicalRegister> {
        self.register_info.physical_registers.get(&number)
    }
    
    /// Add an instruction description
    pub fn add_instruction(&mut self, desc: InstructionDesc) {
        self.instruction_info.instructions.insert(desc.opcode, desc);
    }
    
    /// Get instruction description
    pub fn get_instruction(&self, opcode: u32) -> Option<&InstructionDesc> {
        self.instruction_info.instructions.get(&opcode)
    }
}

/// X86-64 target implementation
pub struct X86_64Target {
    target_machine: TargetMachine,
}

impl X86_64Target {
    /// Create a new X86-64 target
    pub fn new() -> Self {
        let mut target_machine = TargetMachine::new(
            "x86_64-unknown-linux-gnu".to_string(),
            "generic".to_string(),
        );
        
        // Add basic register classes
        let gpr32 = RegisterClass::new(0);
        target_machine.add_register_class(gpr32, RegisterClassInfo {
            name: "GR32".to_string(),
            size_bits: 32,
            alignment_bits: 32,
            registers: vec![0, 1, 2, 3, 4, 5, 6, 7], // EAX, ECX, EDX, EBX, ESP, EBP, ESI, EDI
            allocatable: true,
        });
        
        let gpr64 = RegisterClass::new(1);
        target_machine.add_register_class(gpr64, RegisterClassInfo {
            name: "GR64".to_string(),
            size_bits: 64,
            alignment_bits: 64,
            registers: vec![0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], // RAX-R15
            allocatable: true,
        });
        
        Self { target_machine }
    }
}

impl TargetInfo for X86_64Target {
    fn get_target_machine(&self) -> &TargetMachine {
        &self.target_machine
    }
    
    fn get_register_class(&self, value_type: &str) -> Option<RegisterClass> {
        match value_type {
            "i32" => Some(RegisterClass::new(0)), // GR32
            "i64" => Some(RegisterClass::new(1)), // GR64
            _ => None,
        }
    }
    
    fn get_preferred_alignment(&self, value_type: &str) -> u32 {
        match value_type {
            "i8" => 1,
            "i16" => 2,
            "i32" => 4,
            "i64" => 8,
            "f32" => 4,
            "f64" => 8,
            _ => 1,
        }
    }
    
    fn is_instruction_supported(&self, opcode: u32) -> bool {
        self.target_machine.get_instruction(opcode).is_some()
    }
    
    fn get_instruction_cost(&self, _opcode: u32) -> u32 {
        1 // Simple cost model
    }
    
    fn get_calling_convention(&self) -> CallingConvention {
        CallingConvention {
            argument_registers: vec![7, 6, 2, 1, 8, 9], // RDI, RSI, RDX, RCX, R8, R9
            return_registers: vec![0, 2], // RAX, RDX
            callee_saved_registers: vec![3, 5, 12, 13, 14, 15], // RBX, RBP, R12-R15
            caller_saved_registers: vec![0, 1, 2, 6, 7, 8, 9, 10, 11], // RAX, RCX, RDX, RSI, RDI, R8-R11
            stack_alignment: 16,
        }
    }
}

impl Default for X86_64Target {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_target_machine_creation() {
        let target = TargetMachine::new(
            "x86_64-unknown-linux-gnu".to_string(),
            "generic".to_string(),
        );
        
        assert_eq!(target.triple, "x86_64-unknown-linux-gnu");
        assert_eq!(target.cpu, "generic");
        assert_eq!(target.features.len(), 0);
    }
    
    #[test]
    fn test_x86_64_target() {
        let target = X86_64Target::new();
        
        assert_eq!(target.get_register_class("i32"), Some(RegisterClass::new(0)));
        assert_eq!(target.get_register_class("i64"), Some(RegisterClass::new(1)));
        assert_eq!(target.get_preferred_alignment("i32"), 4);
        assert_eq!(target.get_preferred_alignment("i64"), 8);
    }
    
    #[test]
    fn test_register_class() {
        let rc = RegisterClass::new(42);
        assert_eq!(rc.id(), 42);
    }
}
