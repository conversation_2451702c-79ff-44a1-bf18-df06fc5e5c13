//! # Instruction System - Pattern-Matching Based LLVM IR Instructions
//!
//! This module implements LLVM instructions using <PERSON><PERSON>'s powerful pattern matching
//! system, enabling efficient analysis and transformation of IR code.

use crate::{Value, ValueId, IRError, IRR<PERSON>ult, User};
use std::rc::Rc;

/// Binary operation opcodes
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash)]
pub enum BinaryOpcode {
    // Integer arithmetic
    Add, Sub, Mul, UDiv, SDiv, URem, SRem,
    
    // Bitwise operations
    Shl, LShr, AShr, And, Or, Xor,
    
    // Floating-point arithmetic
    FAdd, FSub, FMul, FDiv, FRem,
}

impl BinaryOpcode {
    /// Check if this is an integer operation
    pub fn is_integer_op(self) -> bool {
        matches!(self, 
            BinaryOpcode::Add | BinaryOpcode::Sub | BinaryOpcode::Mul |
            BinaryOpcode::UDiv | BinaryOpcode::SDiv | BinaryOpcode::URem | BinaryOpcode::SRem |
            BinaryOpcode::Shl | BinaryOpcode::LShr | BinaryOpcode::AShr |
            BinaryOpcode::And | BinaryOpcode::Or | BinaryOpcode::Xor
        )
    }
    
    /// Check if this is a floating-point operation
    pub fn is_float_op(self) -> bool {
        matches!(self, 
            BinaryOpcode::FAdd | BinaryOpcode::FSub | BinaryOpcode::FMul |
            BinaryOpcode::FDiv | BinaryOpcode::FRem
        )
    }
    
    /// Check if this operation is commutative
    pub fn is_commutative(self) -> bool {
        matches!(self,
            BinaryOpcode::Add | BinaryOpcode::Mul | BinaryOpcode::And | BinaryOpcode::Or | BinaryOpcode::Xor |
            BinaryOpcode::FAdd | BinaryOpcode::FMul
        )
    }
    
    /// Get the string representation
    pub fn as_str(self) -> &'static str {
        match self {
            BinaryOpcode::Add => "add",
            BinaryOpcode::Sub => "sub",
            BinaryOpcode::Mul => "mul",
            BinaryOpcode::UDiv => "udiv",
            BinaryOpcode::SDiv => "sdiv",
            BinaryOpcode::URem => "urem",
            BinaryOpcode::SRem => "srem",
            BinaryOpcode::Shl => "shl",
            BinaryOpcode::LShr => "lshr",
            BinaryOpcode::AShr => "ashr",
            BinaryOpcode::And => "and",
            BinaryOpcode::Or => "or",
            BinaryOpcode::Xor => "xor",
            BinaryOpcode::FAdd => "fadd",
            BinaryOpcode::FSub => "fsub",
            BinaryOpcode::FMul => "fmul",
            BinaryOpcode::FDiv => "fdiv",
            BinaryOpcode::FRem => "frem",
        }
    }
}

/// Comparison predicates
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum CompareOpcode {
    // Integer comparisons
    Eq, Ne, Ugt, Uge, Ult, Ule, Sgt, Sge, Slt, Sle,
    
    // Floating-point comparisons
    OEq, OGt, OGe, OLt, OLe, ONe, Ord, Uno, UEq, UGt, UGe, ULt, ULe, UNe, True, False,
}

impl CompareOpcode {
    /// Check if this is an integer comparison
    pub fn is_integer_predicate(self) -> bool {
        matches!(self,
            CompareOpcode::Eq | CompareOpcode::Ne | CompareOpcode::Ugt | CompareOpcode::Uge |
            CompareOpcode::Ult | CompareOpcode::Ule | CompareOpcode::Sgt | CompareOpcode::Sge |
            CompareOpcode::Slt | CompareOpcode::Sle
        )
    }
    
    /// Check if this is a floating-point comparison
    pub fn is_float_predicate(self) -> bool {
        !self.is_integer_predicate()
    }
    
    /// Get the string representation
    pub fn as_str(self) -> &'static str {
        match self {
            CompareOpcode::Eq => "eq",
            CompareOpcode::Ne => "ne",
            CompareOpcode::Ugt => "ugt",
            CompareOpcode::Uge => "uge",
            CompareOpcode::Ult => "ult",
            CompareOpcode::Ule => "ule",
            CompareOpcode::Sgt => "sgt",
            CompareOpcode::Sge => "sge",
            CompareOpcode::Slt => "slt",
            CompareOpcode::Sle => "sle",
            CompareOpcode::OEq => "oeq",
            CompareOpcode::OGt => "ogt",
            CompareOpcode::OGe => "oge",
            CompareOpcode::OLt => "olt",
            CompareOpcode::OLe => "ole",
            CompareOpcode::ONe => "one",
            CompareOpcode::Ord => "ord",
            CompareOpcode::Uno => "uno",
            CompareOpcode::UEq => "ueq",
            CompareOpcode::UGt => "ugt",
            CompareOpcode::UGe => "uge",
            CompareOpcode::ULt => "ult",
            CompareOpcode::ULe => "ule",
            CompareOpcode::UNe => "une",
            CompareOpcode::True => "true",
            CompareOpcode::False => "false",
        }
    }
}

/// Atomic ordering for memory operations
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum AtomicOrdering {
    NotAtomic,
    Unordered,
    Monotonic,
    Acquire,
    Release,
    AcquireRelease,
    SequentiallyConsistent,
}

/// Calling convention for function calls
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum CallingConvention {
    C,
    Fast,
    Cold,
    WebKitJS,
    AnyReg,
    PreserveMost,
    PreserveAll,
    Swift,
    CXXFastTLS,
    Tail,
    CFGuardCheck,
}

/// LLVM IR Instruction types using pattern matching
#[derive(Debug, Clone, PartialEq)]
pub enum Instruction {
    /// Binary arithmetic/logical operations
    BinaryOp {
        id: ValueId,
        op: BinaryOpcode,
        lhs: Value,
        rhs: Value,
        name: Option<String>,
    },
    
    /// Comparison operations
    Compare {
        id: ValueId,
        op: CompareOpcode,
        lhs: Value,
        rhs: Value,
        name: Option<String>,
    },
    
    /// Load from memory
    Load {
        id: ValueId,
        ptr: Value,
        alignment: u32,
        volatile: bool,
        ordering: AtomicOrdering,
        name: Option<String>,
    },
    
    /// Store to memory
    Store {
        value: Value,
        ptr: Value,
        alignment: u32,
        volatile: bool,
        ordering: AtomicOrdering,
    },
    
    /// Function call
    Call {
        id: ValueId,
        func: Value,
        args: Vec<Value>,
        calling_conv: CallingConvention,
        tail_call: bool,
        name: Option<String>,
    },
    
    /// Conditional branch
    Branch {
        condition: Option<Value>, // None for unconditional branch
        true_dest: String,        // Basic block label
        false_dest: Option<String>, // None for unconditional branch
    },
    
    /// Return from function
    Return {
        value: Option<Value>, // None for void return
    },
    
    /// Allocate stack memory
    Alloca {
        id: ValueId,
        ty: std::sync::Arc<crate::Type>,
        array_size: Option<Value>,
        alignment: u32,
        name: Option<String>,
    },

    /// Get element pointer (GEP)
    GetElementPtr {
        id: ValueId,
        ptr: Value,
        indices: Vec<Value>,
        inbounds: bool,
        name: Option<String>,
    },

    /// Type cast operations
    Cast {
        id: ValueId,
        op: CastOpcode,
        value: Value,
        dest_type: std::sync::Arc<crate::Type>,
        name: Option<String>,
    },
    
    /// PHI node for SSA form
    Phi {
        id: ValueId,
        incoming: Vec<(Value, String)>, // (value, basic_block_label)
        name: Option<String>,
    },
    
    /// Select instruction (ternary operator)
    Select {
        id: ValueId,
        condition: Value,
        true_value: Value,
        false_value: Value,
        name: Option<String>,
    },

    /// Vector splat instruction (broadcast scalar to vector)
    VectorSplat {
        id: ValueId,
        scalar: Value,
        vector_type: std::sync::Arc<crate::Type>,
        name: Option<String>,
    },

    /// Shuffle vector instruction
    ShuffleVector {
        id: ValueId,
        vec1: Value,
        vec2: Value,
        mask: Vec<i32>,
        result_type: std::sync::Arc<crate::Type>,
        name: Option<String>,
    },

    /// Extract element from vector
    ExtractElement {
        id: ValueId,
        vector: Value,
        index: Value,
        name: Option<String>,
    },

    /// Insert element into vector
    InsertElement {
        id: ValueId,
        vector: Value,
        element: Value,
        index: Value,
        name: Option<String>,
    },
}

/// Cast operation opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum CastOpcode {
    Trunc, ZExt, SExt, FPToUI, FPToSI, UIToFP, SIToFP,
    FPTrunc, FPExt, PtrToInt, IntToPtr, BitCast, AddrSpaceCast,
}

impl CastOpcode {
    /// Get the string representation
    pub fn as_str(self) -> &'static str {
        match self {
            CastOpcode::Trunc => "trunc",
            CastOpcode::ZExt => "zext",
            CastOpcode::SExt => "sext",
            CastOpcode::FPToUI => "fptoui",
            CastOpcode::FPToSI => "fptosi",
            CastOpcode::UIToFP => "uitofp",
            CastOpcode::SIToFP => "sitofp",
            CastOpcode::FPTrunc => "fptrunc",
            CastOpcode::FPExt => "fpext",
            CastOpcode::PtrToInt => "ptrtoint",
            CastOpcode::IntToPtr => "inttoptr",
            CastOpcode::BitCast => "bitcast",
            CastOpcode::AddrSpaceCast => "addrspacecast",
        }
    }
}

impl Instruction {
    /// Get the unique ID of this instruction (if it produces a value)
    pub fn id(&self) -> Option<ValueId> {
        match self {
            Instruction::BinaryOp { id, .. } |
            Instruction::Compare { id, .. } |
            Instruction::Load { id, .. } |
            Instruction::Call { id, .. } |
            Instruction::Alloca { id, .. } |
            Instruction::GetElementPtr { id, .. } |
            Instruction::Cast { id, .. } |
            Instruction::Phi { id, .. } |
            Instruction::Select { id, .. } |
            Instruction::VectorSplat { id, .. } |
            Instruction::ShuffleVector { id, .. } |
            Instruction::ExtractElement { id, .. } |
            Instruction::InsertElement { id, .. } => Some(*id),
            
            Instruction::Store { .. } |
            Instruction::Branch { .. } |
            Instruction::Return { .. } => None,
        }
    }
    
    /// Get the name of this instruction
    pub fn name(&self) -> Option<&str> {
        match self {
            Instruction::BinaryOp { name, .. } |
            Instruction::Compare { name, .. } |
            Instruction::Load { name, .. } |
            Instruction::Call { name, .. } |
            Instruction::Alloca { name, .. } |
            Instruction::GetElementPtr { name, .. } |
            Instruction::Cast { name, .. } |
            Instruction::Phi { name, .. } |
            Instruction::Select { name, .. } |
            Instruction::VectorSplat { name, .. } |
            Instruction::ShuffleVector { name, .. } |
            Instruction::ExtractElement { name, .. } |
            Instruction::InsertElement { name, .. } => name.as_deref(),
            
            _ => None,
        }
    }
    
    /// Set the name of this instruction
    pub fn set_name(&mut self, new_name: String) {
        match self {
            Instruction::BinaryOp { name, .. } |
            Instruction::Compare { name, .. } |
            Instruction::Load { name, .. } |
            Instruction::Call { name, .. } |
            Instruction::Alloca { name, .. } |
            Instruction::GetElementPtr { name, .. } |
            Instruction::Cast { name, .. } |
            Instruction::Phi { name, .. } |
            Instruction::Select { name, .. } |
            Instruction::VectorSplat { name, .. } |
            Instruction::ShuffleVector { name, .. } |
            Instruction::ExtractElement { name, .. } |
            Instruction::InsertElement { name, .. } => {
                *name = Some(new_name);
            }
            
            _ => {} // Instructions without names
        }
    }
    
    /// Check if this instruction has side effects
    pub fn has_side_effects(&self) -> bool {
        match self {
            Instruction::Store { .. } |
            Instruction::Call { .. } |
            Instruction::Branch { .. } |
            Instruction::Return { .. } => true,
            
            Instruction::Load { volatile, .. } => *volatile,
            
            _ => false,
        }
    }
    
    /// Check if this instruction is a terminator
    pub fn is_terminator(&self) -> bool {
        matches!(self, 
            Instruction::Branch { .. } | 
            Instruction::Return { .. }
        )
    }
    
    /// Check if this instruction produces a value
    pub fn produces_value(&self) -> bool {
        self.id().is_some()
    }

    /// Check if this instruction has users (simplified implementation)
    pub fn has_users(&self) -> bool {
        // In a real implementation, this would check the use-def chains
        // For now, we assume instructions that produce values have users
        // unless they're obviously dead
        match self {
            Instruction::BinaryOp { .. } |
            Instruction::Compare { .. } |
            Instruction::Load { .. } |
            Instruction::Call { .. } |
            Instruction::Alloca { .. } |
            Instruction::GetElementPtr { .. } |
            Instruction::Cast { .. } |
            Instruction::Phi { .. } |
            Instruction::Select { .. } => true, // Assume they have users for now

            _ => false,
        }
    }
    
    /// Get a string representation of this instruction
    pub fn to_string(&self) -> String {
        match self {
            Instruction::BinaryOp { op, lhs, rhs, name, .. } => {
                let result = name.as_deref().unwrap_or("%tmp");
                format!("{} = {} {} {}, {}", result, op.as_str(), 
                       lhs.get_type().to_string(), lhs.to_string(), rhs.to_string())
            }
            
            Instruction::Compare { op, lhs, rhs, name, .. } => {
                let result = name.as_deref().unwrap_or("%tmp");
                format!("{} = icmp {} {} {}, {}", result, op.as_str(),
                       lhs.get_type().to_string(), lhs.to_string(), rhs.to_string())
            }
            
            Instruction::Load { ptr, name, .. } => {
                let result = name.as_deref().unwrap_or("%tmp");
                format!("{} = load {}, {} {}", result,
                       ptr.get_type().element_type().unwrap().to_string(),
                       ptr.get_type().to_string(), ptr.to_string())
            }
            
            Instruction::Store { value, ptr, .. } => {
                format!("store {} {}, {} {}", 
                       value.get_type().to_string(), value.to_string(),
                       ptr.get_type().to_string(), ptr.to_string())
            }
            
            Instruction::Call { func, args, name, .. } => {
                let result = if let Some(name) = name {
                    format!("{} = ", name)
                } else {
                    String::new()
                };
                let args_str = args.iter()
                    .map(|arg| format!("{} {}", arg.get_type().to_string(), arg.to_string()))
                    .collect::<Vec<_>>()
                    .join(", ");
                format!("{}call {} {}({})", result, 
                       func.get_type().to_string(), func.to_string(), args_str)
            }
            
            Instruction::Branch { condition: Some(cond), true_dest, false_dest, .. } => {
                format!("br i1 {}, label %{}, label %{}", 
                       cond.to_string(), true_dest, false_dest.as_ref().unwrap())
            }
            
            Instruction::Branch { condition: None, true_dest, .. } => {
                format!("br label %{}", true_dest)
            }
            
            Instruction::Return { value: Some(val) } => {
                format!("ret {} {}", val.get_type().to_string(), val.to_string())
            }
            
            Instruction::Return { value: None } => {
                "ret void".to_string()
            }
            
            _ => format!("{:?}", self), // Fallback for other instructions
        }
    }
}

impl User for Instruction {
    fn get_operands(&self) -> Vec<Value> {
        match self {
            Instruction::BinaryOp { lhs, rhs, .. } => vec![lhs.clone(), rhs.clone()],
            Instruction::Compare { lhs, rhs, .. } => vec![lhs.clone(), rhs.clone()],
            Instruction::Load { ptr, .. } => vec![ptr.clone()],
            Instruction::Store { value, ptr, .. } => vec![value.clone(), ptr.clone()],
            Instruction::Call { func, args, .. } => {
                let mut operands = vec![func.clone()];
                operands.extend(args.clone());
                operands
            }
            Instruction::Branch { condition: Some(cond), .. } => vec![cond.clone()],
            Instruction::Branch { condition: None, .. } => vec![],
            Instruction::Return { value: Some(val) } => vec![val.clone()],
            Instruction::Return { value: None } => vec![],
            Instruction::Alloca { array_size: Some(size), .. } => vec![size.clone()],
            Instruction::Alloca { array_size: None, .. } => vec![],
            Instruction::GetElementPtr { ptr, indices, .. } => {
                let mut operands = vec![ptr.clone()];
                operands.extend(indices.clone());
                operands
            }
            Instruction::Cast { value, .. } => vec![value.clone()],
            Instruction::Phi { incoming, .. } => {
                incoming.iter().map(|(val, _)| val.clone()).collect()
            }
            Instruction::Select { condition, true_value, false_value, .. } => {
                vec![condition.clone(), true_value.clone(), false_value.clone()]
            }
            Instruction::VectorSplat { scalar, .. } => vec![scalar.clone()],
            Instruction::ShuffleVector { vec1, vec2, .. } => vec![vec1.clone(), vec2.clone()],
            Instruction::ExtractElement { vector, index, .. } => vec![vector.clone(), index.clone()],
            Instruction::InsertElement { vector, element, index, .. } => {
                vec![vector.clone(), element.clone(), index.clone()]
            }
        }
    }
    
    fn set_operand(&mut self, index: usize, value: Value) -> IRResult<()> {
        match self {
            Instruction::BinaryOp { lhs, rhs, .. } => {
                match index {
                    0 => *lhs = value,
                    1 => *rhs = value,
                    _ => return Err(IRError::InvalidPosition),
                }
            }
            Instruction::Compare { lhs, rhs, .. } => {
                match index {
                    0 => *lhs = value,
                    1 => *rhs = value,
                    _ => return Err(IRError::InvalidPosition),
                }
            }
            Instruction::Load { ptr, .. } => {
                if index == 0 {
                    *ptr = value;
                } else {
                    return Err(IRError::InvalidPosition);
                }
            }
            Instruction::Store { value: val, ptr, .. } => {
                match index {
                    0 => *val = value,
                    1 => *ptr = value,
                    _ => return Err(IRError::InvalidPosition),
                }
            }
            // Add other instruction types as needed
            _ => return Err(IRError::InvalidOperation("Operand setting not implemented for this instruction type".to_string())),
        }
        Ok(())
    }
    
    fn get_num_operands(&self) -> usize {
        self.get_operands().len()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{Context, Value};
    
    #[test]
    fn test_instruction_creation() {
        let context = Context::new();
        let i32_type = context.get_integer_type(32);
        
        let lhs = Value::constant_int(10, i32_type.clone());
        let rhs = Value::constant_int(20, i32_type);
        
        let add_inst = Instruction::BinaryOp {
            id: ValueId::new(),
            op: BinaryOpcode::Add,
            lhs,
            rhs,
            name: Some("result".to_string()),
        };
        
        assert!(add_inst.produces_value());
        assert!(!add_inst.has_side_effects());
        assert!(!add_inst.is_terminator());
        assert_eq!(add_inst.name(), Some("result"));
    }
    
    #[test]
    fn test_instruction_pattern_matching() {
        let context = Context::new();
        let i32_type = context.get_integer_type(32);
        
        let val = Value::constant_int(42, i32_type);
        
        let instructions = vec![
            Instruction::Return { value: Some(val.clone()) },
            Instruction::Return { value: None },
        ];
        
        for inst in instructions {
            match inst {
                Instruction::Return { value: Some(_) } => {
                    // Handle return with value
                }
                Instruction::Return { value: None } => {
                    // Handle void return
                }
                _ => panic!("Unexpected instruction type"),
            }
        }
    }
    
    #[test]
    fn test_user_trait() {
        let context = Context::new();
        let i32_type = context.get_integer_type(32);
        
        let lhs = Value::constant_int(10, i32_type.clone());
        let rhs = Value::constant_int(20, i32_type);
        
        let mut add_inst = Instruction::BinaryOp {
            id: ValueId::new(),
            op: BinaryOpcode::Add,
            lhs: lhs.clone(),
            rhs: rhs.clone(),
            name: None,
        };
        
        assert_eq!(add_inst.get_num_operands(), 2);
        
        let operands = add_inst.get_operands();
        assert_eq!(operands[0], lhs);
        assert_eq!(operands[1], rhs);
        
        // Test operand replacement
        let new_val = Value::constant_int(30, context.get_integer_type(32));
        assert!(add_inst.set_operand(1, new_val.clone()).is_ok());
        
        let updated_operands = add_inst.get_operands();
        assert_eq!(updated_operands[1], new_val);
    }
}

// Note: Send and Sync implementations would be needed for parallel processing
// but are disabled due to #![forbid(unsafe_code)] directive
