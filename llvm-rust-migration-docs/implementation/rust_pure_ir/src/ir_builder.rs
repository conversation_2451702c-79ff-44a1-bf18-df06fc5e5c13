//! Enhanced IR Builder for Pure Rust LLVM IR
//! 
//! This module provides type-safe, memory-safe IR construction capabilities
//! with compile-time guarantees and zero-cost abstractions.

#![forbid(unsafe_code)]

use crate::*;
use std::collections::HashMap;
use std::rc::{Rc, Weak};
use std::sync::Arc;
use std::cell::RefCell;

/// Enhanced IR Builder with type safety and performance
pub struct IRBuilder {
    context: Arc<Context>,
    current_function: Option<Rc<RefCell<Function>>>,
    current_block: Option<Rc<RefCell<BasicBlock>>>,
    insert_point: Option<usize>,
    debug_info: Option<DebugInfo>,
    fast_math_flags: FastMathFlags,
}

impl IRBuilder {
    /// Create a new IR Builder
    pub fn new(context: Arc<Context>) -> Self {
        Self {
            context,
            current_function: None,
            current_block: None,
            insert_point: None,
            debug_info: None,
            fast_math_flags: FastMathFlags::default(),
        }
    }
    
    /// Set the current insertion point
    pub fn set_insert_point(&mut self, block: Rc<RefCell<BasicBlock>>, position: Option<usize>) {
        self.current_block = Some(block);
        self.insert_point = position;
    }
    
    /// Get the current context
    pub fn get_context(&self) -> &Arc<Context> {
        &self.context
    }
    
    /// Set fast math flags for floating point operations
    pub fn set_fast_math_flags(&mut self, flags: FastMathFlags) {
        self.fast_math_flags = flags;
    }
    
    // === Function Creation ===
    
    /// Create a new function with type safety
    pub fn create_function(&mut self, name: &str, ty: Rc<Type>) -> Result<Rc<RefCell<Function>>, IRError> {
        if !matches!(*ty, Type::Function { .. }) {
            return Err(IRError::InvalidFunctionType);
        }
        
        let func = Rc::new(RefCell::new(Function::new(name.to_string(), ty)));
        self.current_function = Some(func.clone());
        Ok(func)
    }
    
    /// Create a new basic block in the current function
    pub fn create_basic_block(&mut self, name: &str) -> Result<Rc<RefCell<BasicBlock>>, IRError> {
        let function = self.current_function
            .as_ref()
            .ok_or(IRError::NoCurrentFunction)?;
        
        let block = Rc::new(RefCell::new(BasicBlock::new(Some(name.to_string()))));
        function.borrow_mut().add_basic_block(block.clone())?;
        
        self.current_block = Some(block.clone());
        self.insert_point = Some(0);
        Ok(block)
    }
    
    // === Arithmetic Instructions ===
    
    /// Create an add instruction with type safety
    pub fn create_add(&mut self, lhs: Value, rhs: Value, name: &str) -> Result<Value, IRError> {
        // Compile-time type checking
        if lhs.get_type() != rhs.get_type() {
            return Err(IRError::TypeMismatch {
                expected: lhs.get_type(),
                found: rhs.get_type(),
            });
        }
        
        if !lhs.get_type().is_integer() && !lhs.get_type().is_float() {
            return Err(IRError::InvalidOperandType);
        }
        
        let inst = Instruction::BinaryOp {
            op: BinaryOpcode::Add,
            lhs,
            rhs,
            flags: ArithmeticFlags::default(),
        };
        
        self.insert_instruction(inst, Some(name.to_string()))
    }
    
    /// Create a subtract instruction
    pub fn create_sub(&mut self, lhs: Value, rhs: Value, name: &str) -> Result<Value, IRError> {
        self.create_binary_op(BinaryOpcode::Sub, lhs, rhs, name)
    }
    
    /// Create a multiply instruction
    pub fn create_mul(&mut self, lhs: Value, rhs: Value, name: &str) -> Result<Value, IRError> {
        self.create_binary_op(BinaryOpcode::Mul, lhs, rhs, name)
    }
    
    /// Create a divide instruction
    pub fn create_div(&mut self, lhs: Value, rhs: Value, name: &str) -> Result<Value, IRError> {
        self.create_binary_op(BinaryOpcode::Div, lhs, rhs, name)
    }
    
    /// Generic binary operation creation
    fn create_binary_op(&mut self, op: BinaryOpcode, lhs: Value, rhs: Value, name: &str) -> Result<Value, IRError> {
        // Type checking
        if lhs.get_type() != rhs.get_type() {
            return Err(IRError::TypeMismatch {
                expected: lhs.get_type(),
                found: rhs.get_type(),
            });
        }
        
        let inst = Instruction::BinaryOp {
            op,
            lhs,
            rhs,
            flags: ArithmeticFlags::default(),
        };
        
        self.insert_instruction(inst, Some(name.to_string()))
    }
    
    // === Memory Operations ===
    
    /// Create an alloca instruction
    pub fn create_alloca(&mut self, ty: Rc<Type>, name: &str) -> Result<Value, IRError> {
        let inst = Instruction::Alloca {
            ty: ty.clone(),
            array_size: None,
            alignment: ty.get_alignment(),
        };
        
        self.insert_instruction(inst, Some(name.to_string()))
    }
    
    /// Create a load instruction with type safety
    pub fn create_load(&mut self, ptr: Value, name: &str) -> Result<Value, IRError> {
        let ptr_ty = ptr.get_type();
        let _pointee_ty = match &*ptr_ty {
            Type::Pointer { pointee, .. } => pointee.clone(),
            _ => return Err(IRError::ExpectedPointerType),
        };
        
        let inst = Instruction::Load {
            ptr,
            alignment: 0, // Will be computed based on type
            volatile: false,
            ordering: AtomicOrdering::NotAtomic,
            metadata: HashMap::new(),
        };
        
        self.insert_instruction(inst, Some(name.to_string()))
    }
    
    /// Create a store instruction with type safety
    pub fn create_store(&mut self, value: Value, ptr: Value) -> Result<(), IRError> {
        let ptr_ty = ptr.get_type();
        let pointee_ty = match &*ptr_ty {
            Type::Pointer { pointee, .. } => pointee,
            _ => return Err(IRError::ExpectedPointerType),
        };
        
        if value.get_type() != Rc::new((**pointee_ty).clone()) {
            return Err(IRError::TypeMismatch {
                expected: Rc::new((**pointee_ty).clone()),
                found: value.get_type(),
            });
        }
        
        let inst = Instruction::Store {
            value,
            ptr,
            alignment: pointee_ty.get_alignment(),
            volatile: false,
            ordering: AtomicOrdering::NotAtomic,
        };
        
        self.insert_instruction(inst, None)?;
        Ok(())
    }
    
    // === Control Flow ===
    
    /// Create a conditional branch
    pub fn create_conditional_branch(
        &mut self,
        condition: Value,
        true_block: Rc<RefCell<BasicBlock>>,
        false_block: Rc<RefCell<BasicBlock>>,
    ) -> Result<(), IRError> {
        if !condition.get_type().is_integer_with_bits(1) {
            return Err(IRError::ExpectedBooleanType);
        }
        
        let inst = Instruction::Branch {
            condition: Some(condition),
            true_block,
            false_block: Some(false_block),
        };
        
        self.insert_instruction(inst, None)?;
        Ok(())
    }
    
    /// Create an unconditional branch
    pub fn create_branch(&mut self, target_block: Rc<RefCell<BasicBlock>>) -> Result<(), IRError> {
        let inst = Instruction::Branch {
            condition: None,
            true_block: target_block,
            false_block: None,
        };
        
        self.insert_instruction(inst, None)?;
        Ok(())
    }
    
    /// Create a return instruction
    pub fn create_return(&mut self, value: Option<Value>) -> Result<(), IRError> {
        let inst = Instruction::Return { value };
        self.insert_instruction(inst, None)?;
        Ok(())
    }
    
    // === Function Calls ===
    
    /// Create a function call with type safety
    pub fn create_call(
        &mut self,
        func: Value,
        args: Vec<Value>,
        name: &str,
    ) -> Result<Value, IRError> {
        let func_ty = func.get_type();
        let (param_types, _return_type) = match &*func_ty {
            Type::Function { params, ret, .. } => (params, ret),
            _ => return Err(IRError::ExpectedFunctionType),
        };
        
        // Validate argument types
        if args.len() != param_types.len() {
            return Err(IRError::ArgumentCountMismatch);
        }
        
        for (arg, param_ty) in args.iter().zip(param_types.iter()) {
            if arg.get_type() != Rc::new(param_ty.clone()) {
                return Err(IRError::ArgumentTypeMismatch);
            }
        }
        
        let inst = Instruction::Call {
            func,
            args,
            calling_conv: CallingConvention::C,
            tail_call: false,
            attributes: CallAttributes::default(),
        };
        
        self.insert_instruction(inst, Some(name.to_string()))
    }
    
    // === Comparison Operations ===
    
    /// Create an integer comparison
    pub fn create_icmp(
        &mut self,
        predicate: ICmpPredicate,
        lhs: Value,
        rhs: Value,
        name: &str,
    ) -> Result<Value, IRError> {
        if lhs.get_type() != rhs.get_type() {
            return Err(IRError::TypeMismatch {
                expected: lhs.get_type(),
                found: rhs.get_type(),
            });
        }
        
        if !lhs.get_type().is_integer() {
            return Err(IRError::InvalidOperandType);
        }
        
        let inst = Instruction::ICmp {
            predicate,
            lhs,
            rhs,
        };
        
        self.insert_instruction(inst, Some(name.to_string()))
    }
    
    // === Helper Methods ===
    
    /// Insert an instruction at the current insertion point
    fn insert_instruction(&mut self, inst: Instruction, name: Option<String>) -> Result<Value, IRError> {
        let block = self.current_block
            .as_ref()
            .ok_or(IRError::NoCurrentBlock)?;
        
        let insert_pos = self.insert_point.unwrap_or(0);
        
        // Create value for instruction result
        let result_ty = inst.get_result_type()?;
        let value = Value::new(result_ty, name);
        
        // Insert instruction
        block.borrow_mut().insert_instruction(inst, insert_pos)?;
        
        // Update insert point
        self.insert_point = Some(insert_pos + 1);
        
        Ok(value)
    }
}

/// Debug information for IR construction
#[derive(Debug, Clone)]
pub struct DebugInfo {
    pub file: String,
    pub line: u32,
    pub column: u32,
}

/// Extended IR Error types for IR Builder
#[derive(Debug, Clone)]
pub enum IRBuilderError {
    NoCurrentFunction,
    NoCurrentBlock,
    InvalidInsertionPoint,
    TypeMismatch { expected: Rc<Type>, found: Rc<Type> },
    InvalidOperandType,
    ExpectedPointerType,
    ExpectedFunctionType,
    ExpectedBooleanType,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_ir_builder_creation() {
        let context = Arc::new(Context::new());
        let builder = IRBuilder::new(context.clone());
        
        assert!(builder.current_function.is_none());
        assert!(builder.current_block.is_none());
    }
    
    #[test]
    fn test_function_creation() {
        let context = Arc::new(Context::new());
        let mut builder = IRBuilder::new(context.clone());
        
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty, vec![]);
        
        let function = builder.create_function("test_func", func_ty).unwrap();
        assert_eq!(function.borrow().get_name(), "test_func");
        assert!(builder.current_function.is_some());
    }
    
    #[test]
    fn test_basic_block_creation() {
        let context = Arc::new(Context::new());
        let mut builder = IRBuilder::new(context.clone());
        
        // Create function first
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty, vec![]);
        let _function = builder.create_function("test_func", func_ty).unwrap();
        
        // Create basic block
        let block = builder.create_basic_block("entry").unwrap();
        assert!(builder.current_block.is_some());
        assert_eq!(builder.insert_point, Some(0));
    }
    
    #[test]
    fn test_arithmetic_instructions() {
        let context = Arc::new(Context::new());
        let mut builder = IRBuilder::new(context.clone());
        
        let int32_ty = context.get_integer_type(32);
        let lhs = Value::new(int32_ty.clone(), Some("lhs".to_string()));
        let rhs = Value::new(int32_ty.clone(), Some("rhs".to_string()));
        
        // Setup function and block
        let func_ty = context.get_function_type(int32_ty.clone(), vec![]);
        let _function = builder.create_function("test_func", func_ty).unwrap();
        let _block = builder.create_basic_block("entry").unwrap();
        
        // Test add instruction
        let result = builder.create_add(lhs, rhs, "sum").unwrap();
        assert_eq!(result.get_name(), Some("sum"));
    }
}
