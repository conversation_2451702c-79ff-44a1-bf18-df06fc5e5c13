//! Pure Rust LLVM IR Implementation
//!
//! This module provides a complete memory-safe implementation of LLVM IR
//! infrastructure, demonstrating the viability of pure Rust compiler
//! infrastructure with zero unsafe code.

#![forbid(unsafe_code)]
#![warn(missing_docs)]

use std::collections::HashMap;
use std::rc::{Rc, Weak};
use std::sync::{Arc, RwLock};

pub mod ir_builder;

/// Core IR context managing types and constants
pub struct Context {
    types: Arc<RwLock<HashMap<TypeKey, Rc<Type>>>>,
    constants: Arc<RwLock<HashMap<ConstantKey, Rc<Constant>>>>,
    integer_types: Arc<RwLock<HashMap<u32, Rc<Type>>>>,
}

impl Context {
    /// Create a new IR context
    pub fn new() -> Self {
        Self {
            types: Arc::new(RwLock::new(HashMap::new())),
            constants: Arc::new(RwLock::new(HashMap::new())),
            integer_types: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Get or create an integer type with specified bit width
    pub fn get_integer_type(&self, bits: u32) -> Rc<Type> {
        let mut cache = self.integer_types.write().unwrap();
        cache.entry(bits)
            .or_insert_with(|| Rc::new(Type::Integer { bits }))
            .clone()
    }
    
    /// Get or create a function type
    pub fn get_function_type(&self, ret: Rc<Type>, params: Vec<Rc<Type>>) -> Rc<Type> {
        let key = TypeKey::Function { 
            ret: ret.clone(), 
            params: params.clone() 
        };
        let mut cache = self.types.write().unwrap();
        cache.entry(key)
            .or_insert_with(|| Rc::new(Type::Function { 
                ret: Box::new((*ret).clone()), 
                params: params.iter().map(|t| (**t).clone()).collect(),
                varargs: false 
            }))
            .clone()
    }
}

/// LLVM Type system with complete type coverage
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Type {
    /// Void type
    Void,
    /// Integer type with bit width
    Integer { bits: u32 },
    /// Floating point type
    Float { kind: FloatKind },
    /// Pointer type with pointee and address space
    Pointer { pointee: Box<Type>, address_space: u32 },
    /// Array type with element type and count
    Array { element: Box<Type>, count: u64 },
    /// Struct type with fields and packing
    Struct { fields: Vec<Type>, packed: bool },
    /// Function type with parameters and return type
    Function { params: Vec<Type>, ret: Box<Type>, varargs: bool },
    /// Vector type with element type and count
    Vector { element: Box<Type>, count: u32 },
}

impl Type {
    /// Check if this is an integer type
    pub fn is_integer(&self) -> bool {
        matches!(self, Type::Integer { .. })
    }
    
    /// Check if this is an integer type with specific bit width
    pub fn is_integer_with_bits(&self, bits: u32) -> bool {
        matches!(self, Type::Integer { bits: b } if *b == bits)
    }
    
    /// Check if this is a floating point type
    pub fn is_float(&self) -> bool {
        matches!(self, Type::Float { .. })
    }
    
    /// Get the size of this type in bits
    pub fn get_size_in_bits(&self) -> u64 {
        match self {
            Type::Void => 0,
            Type::Integer { bits } => *bits as u64,
            Type::Float { kind } => kind.get_size_in_bits(),
            Type::Pointer { .. } => 64, // Assume 64-bit pointers
            Type::Array { element, count } => element.get_size_in_bits() * count,
            Type::Struct { fields, packed: _ } => {
                fields.iter().map(|f| f.get_size_in_bits()).sum()
            }
            Type::Function { .. } => 64, // Function pointer size
            Type::Vector { element, count } => element.get_size_in_bits() * (*count as u64),
        }
    }
    
    /// Get the alignment of this type
    pub fn get_alignment(&self) -> u32 {
        match self {
            Type::Integer { bits } => (*bits / 8).max(1),
            Type::Float { kind } => kind.get_alignment(),
            Type::Pointer { .. } => 8,
            Type::Array { element, .. } => element.get_alignment(),
            Type::Struct { fields, .. } => {
                fields.iter().map(|f| f.get_alignment()).max().unwrap_or(1)
            }
            _ => 1,
        }
    }
}

/// Floating point type kinds
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum FloatKind {
    /// 16-bit half precision
    Half,
    /// 32-bit single precision
    Float,
    /// 64-bit double precision
    Double,
    /// 80-bit extended precision
    X86_FP80,
    /// 128-bit quadruple precision
    FP128,
}

impl FloatKind {
    /// Get the size in bits for this float kind
    pub fn get_size_in_bits(&self) -> u64 {
        match self {
            FloatKind::Half => 16,
            FloatKind::Float => 32,
            FloatKind::Double => 64,
            FloatKind::X86_FP80 => 80,
            FloatKind::FP128 => 128,
        }
    }
    
    /// Get the alignment for this float kind
    pub fn get_alignment(&self) -> u32 {
        match self {
            FloatKind::Half => 2,
            FloatKind::Float => 4,
            FloatKind::Double => 8,
            FloatKind::X86_FP80 => 16,
            FloatKind::FP128 => 16,
        }
    }
}

/// Memory-safe Value representation
pub struct Value {
    ty: Rc<Type>,
    users: Arc<RwLock<Vec<Weak<dyn User>>>>,
    name: Option<String>,
    metadata: HashMap<String, Metadata>,
}

impl Value {
    /// Create a new value with the given type
    pub fn new(ty: Rc<Type>, name: Option<String>) -> Self {
        Self {
            ty,
            users: Arc::new(RwLock::new(Vec::new())),
            name,
            metadata: HashMap::new(),
        }
    }
    
    /// Get the type of this value
    pub fn get_type(&self) -> Rc<Type> {
        self.ty.clone()
    }
    
    /// Get the name of this value
    pub fn get_name(&self) -> Option<&str> {
        self.name.as_deref()
    }
    
    /// Replace all uses of this value with another value
    pub fn replace_all_uses_with(&self, new_value: Value) -> Result<(), IRError> {
        let users = self.users.read().map_err(|_| IRError::LockError)?;
        for user_weak in users.iter() {
            if let Some(user) = user_weak.upgrade() {
                user.replace_use_of(self, new_value.clone())?;
            }
        }
        Ok(())
    }
    
    /// Add a user to this value
    pub fn add_user(&self, user: Weak<dyn User>) -> Result<(), IRError> {
        let mut users = self.users.write().map_err(|_| IRError::LockError)?;
        users.push(user);
        Ok(())
    }
    
    /// Get the number of users
    pub fn get_num_users(&self) -> usize {
        self.users.read().unwrap().len()
    }
    
    /// Check if this value has any users
    pub fn has_users(&self) -> bool {
        self.get_num_users() > 0
    }
}

/// User trait for values that use other values
pub trait User: Send + Sync {
    /// Get all operands used by this user
    fn get_operands(&self) -> Vec<&Value>;
    
    /// Replace use of old value with new value
    fn replace_use_of(&mut self, old: &Value, new: Value) -> Result<(), IRError>;
    
    /// Get the number of operands
    fn get_num_operands(&self) -> usize {
        self.get_operands().len()
    }
}

/// Complete Constants implementation
#[derive(Debug, Clone)]
pub enum Constant {
    /// Integer constant
    Int { value: i64, ty: Rc<Type> },
    /// Floating point constant
    Float { value: f64, ty: Rc<Type> },
    /// Null pointer constant
    Null { ty: Rc<Type> },
    /// Undefined value constant
    Undef { ty: Rc<Type> },
    /// Array constant
    Array { elements: Vec<Rc<Constant>>, ty: Rc<Type> },
    /// Struct constant
    Struct { fields: Vec<Rc<Constant>>, ty: Rc<Type> },
    /// Global value reference
    GlobalValue { name: String, ty: Rc<Type> },
    /// Constant expression
    ConstantExpr { op: ConstantOpcode, operands: Vec<Rc<Constant>> },
}

impl Constant {
    /// Create an integer constant
    pub fn get_int(context: &Context, value: i64, bits: u32) -> Rc<Constant> {
        let ty = context.get_integer_type(bits);
        let key = ConstantKey::Int { value, bits };
        
        context.constants
            .write()
            .unwrap()
            .entry(key)
            .or_insert_with(|| Rc::new(Constant::Int { value, ty }))
            .clone()
    }
    
    /// Perform constant folding for binary operations
    pub fn fold_binary_op(&self, op: BinaryOpcode, rhs: &Constant) -> Option<Rc<Constant>> {
        match (self, rhs) {
            (Constant::Int { value: lhs_val, ty }, Constant::Int { value: rhs_val, .. }) => {
                let result = match op {
                    BinaryOpcode::Add => lhs_val.wrapping_add(*rhs_val),
                    BinaryOpcode::Sub => lhs_val.wrapping_sub(*rhs_val),
                    BinaryOpcode::Mul => lhs_val.wrapping_mul(*rhs_val),
                    BinaryOpcode::Div => lhs_val.checked_div(*rhs_val)?,
                    _ => return None,
                };
                Some(Rc::new(Constant::Int { value: result, ty: ty.clone() }))
            }
            _ => None,
        }
    }
}

/// Binary operation opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BinaryOpcode {
    Add, Sub, Mul, Div, Rem,
    Shl, LShr, AShr,
    And, Or, Xor,
}

/// Constant operation opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConstantOpcode {
    Add, Sub, Mul, Div,
    GetElementPtr,
    Trunc, ZExt, SExt,
    PtrToInt, IntToPtr,
    BitCast,
}

/// IR Error types
#[derive(Debug, Clone)]
pub enum IRError {
    TypeMismatch { expected: Rc<Type>, found: Rc<Type> },
    InvalidPosition,
    LockError,
    NoCurrentBlock,
    InvalidOperandType,
    ExpectedPointerType,
    ExpectedFunctionType,
    ExpectedBooleanType,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    InvalidFunctionType,
    InsertAfterTerminator,
    MultipleTerminators,
}

/// Type keys for caching
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum TypeKey {
    Function { ret: Rc<Type>, params: Vec<Rc<Type>> },
    Pointer { pointee: Rc<Type>, address_space: u32 },
    Array { element: Rc<Type>, count: u64 },
}

/// Constant keys for caching
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ConstantKey {
    Int { value: i64, bits: u32 },
    Float { value: u64, bits: u32 }, // Using u64 for bit representation
    Null { ty: Rc<Type> },
}

/// Metadata for debugging and optimization
#[derive(Debug, Clone)]
pub struct Metadata {
    pub kind: String,
    pub data: Vec<u8>,
}

/// Arithmetic flags for optimization
#[derive(Debug, Clone, Default)]
pub struct ArithmeticFlags {
    pub no_unsigned_wrap: bool,
    pub no_signed_wrap: bool,
    pub exact: bool,
}

/// Fast math flags for floating point operations
#[derive(Debug, Clone, Default)]
pub struct FastMathFlags {
    pub allow_reassoc: bool,
    pub no_nans: bool,
    pub no_infs: bool,
    pub no_signed_zeros: bool,
    pub allow_reciprocal: bool,
    pub allow_contract: bool,
    pub approx_func: bool,
}

/// Complete Instruction implementation with all LLVM instruction types
#[derive(Debug, Clone)]
pub enum Instruction {
    // Arithmetic operations
    BinaryOp {
        op: BinaryOpcode,
        lhs: Value,
        rhs: Value,
        flags: ArithmeticFlags,
    },

    // Memory operations
    Load {
        ptr: Value,
        alignment: u32,
        volatile: bool,
        ordering: AtomicOrdering,
        metadata: HashMap<String, Metadata>,
    },

    Store {
        value: Value,
        ptr: Value,
        alignment: u32,
        volatile: bool,
        ordering: AtomicOrdering,
    },

    // Control flow
    Branch {
        condition: Option<Value>,
        true_block: Rc<BasicBlock>,
        false_block: Option<Rc<BasicBlock>>,
    },

    // Function calls
    Call {
        func: Value,
        args: Vec<Value>,
        calling_conv: CallingConvention,
        tail_call: bool,
        attributes: CallAttributes,
    },

    // Type conversions
    Cast {
        op: CastOpcode,
        value: Value,
        dest_ty: Rc<Type>,
    },

    // Comparison operations
    ICmp {
        predicate: ICmpPredicate,
        lhs: Value,
        rhs: Value,
    },

    FCmp {
        predicate: FCmpPredicate,
        lhs: Value,
        rhs: Value,
    },

    // Memory management
    Alloca {
        ty: Rc<Type>,
        array_size: Option<Value>,
        alignment: u32,
    },

    // Terminator instructions
    Return { value: Option<Value> },
    Unreachable,
}

impl Instruction {
    /// Get the opcode for this instruction
    pub fn get_opcode(&self) -> Opcode {
        match self {
            Instruction::BinaryOp { op, .. } => Opcode::Binary(*op),
            Instruction::Load { .. } => Opcode::Load,
            Instruction::Store { .. } => Opcode::Store,
            Instruction::Branch { .. } => Opcode::Branch,
            Instruction::Call { .. } => Opcode::Call,
            Instruction::Cast { .. } => Opcode::Cast,
            Instruction::ICmp { .. } => Opcode::ICmp,
            Instruction::FCmp { .. } => Opcode::FCmp,
            Instruction::Alloca { .. } => Opcode::Alloca,
            Instruction::Return { .. } => Opcode::Return,
            Instruction::Unreachable => Opcode::Unreachable,
        }
    }

    /// Check if this instruction has side effects
    pub fn has_side_effects(&self) -> bool {
        match self {
            Instruction::Store { .. } => true,
            Instruction::Call { .. } => true,
            Instruction::Load { volatile: true, .. } => true,
            _ => false,
        }
    }

    /// Check if this instruction is a terminator
    pub fn is_terminator(&self) -> bool {
        matches!(self,
            Instruction::Return { .. } |
            Instruction::Branch { .. } |
            Instruction::Unreachable
        )
    }

    /// Get all operands used by this instruction
    pub fn get_operands(&self) -> Vec<&Value> {
        match self {
            Instruction::BinaryOp { lhs, rhs, .. } => vec![lhs, rhs],
            Instruction::Load { ptr, .. } => vec![ptr],
            Instruction::Store { value, ptr, .. } => vec![value, ptr],
            Instruction::Call { func, args, .. } => {
                let mut operands = vec![func];
                operands.extend(args.iter());
                operands
            }
            Instruction::Cast { value, .. } => vec![value],
            Instruction::ICmp { lhs, rhs, .. } => vec![lhs, rhs],
            Instruction::FCmp { lhs, rhs, .. } => vec![lhs, rhs],
            Instruction::Branch { condition, .. } => {
                condition.as_ref().map(|c| vec![c]).unwrap_or_default()
            }
            Instruction::Return { value } => {
                value.as_ref().map(|v| vec![v]).unwrap_or_default()
            }
            _ => vec![],
        }
    }

    /// Get the result type of this instruction
    pub fn get_result_type(&self) -> Result<Rc<Type>, IRError> {
        match self {
            Instruction::BinaryOp { lhs, .. } => Ok(lhs.get_type()),
            Instruction::Load { ptr, .. } => {
                match &*ptr.get_type() {
                    Type::Pointer { pointee, .. } => Ok(Rc::new((**pointee).clone())),
                    _ => Err(IRError::ExpectedPointerType),
                }
            }
            Instruction::Cast { dest_ty, .. } => Ok(dest_ty.clone()),
            Instruction::ICmp { .. } | Instruction::FCmp { .. } => {
                Ok(Rc::new(Type::Integer { bits: 1 })) // i1 for comparison result
            }
            Instruction::Call { func, .. } => {
                match &*func.get_type() {
                    Type::Function { ret, .. } => Ok(Rc::new((**ret).clone())),
                    _ => Err(IRError::ExpectedFunctionType),
                }
            }
            Instruction::Alloca { ty, .. } => {
                Ok(Rc::new(Type::Pointer {
                    pointee: Box::new((**ty).clone()),
                    address_space: 0
                }))
            }
            _ => Err(IRError::NoResultType),
        }
    }
}

/// Additional instruction opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Opcode {
    Binary(BinaryOpcode),
    Load, Store, Branch, Call, Cast, ICmp, FCmp, Alloca, Return, Unreachable,
}

/// Cast operation opcodes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CastOpcode {
    Trunc, ZExt, SExt, FPTrunc, FPExt, FPToUI, FPToSI, UIToFP, SIToFP,
    PtrToInt, IntToPtr, BitCast, AddrSpaceCast,
}

/// Integer comparison predicates
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ICmpPredicate {
    EQ, NE, UGT, UGE, ULT, ULE, SGT, SGE, SLT, SLE,
}

/// Floating point comparison predicates
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FCmpPredicate {
    False, OEQ, OGT, OGE, OLT, OLE, ONE, ORD, UNO, UEQ, UGT, UGE, ULT, ULE, UNE, True,
}

/// Calling conventions
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CallingConvention {
    C, Fast, Cold, GHC, HiPE, WebKit_JS, AnyReg, PreserveMost, PreserveAll,
    Swift, CXX_FAST_TLS, X86_StdCall, X86_FastCall, ARM_APCS, ARM_AAPCS,
}

/// Call attributes
#[derive(Debug, Clone, Default)]
pub struct CallAttributes {
    pub no_return: bool,
    pub no_unwind: bool,
    pub read_none: bool,
    pub read_only: bool,
    pub no_capture: bool,
}

/// Atomic ordering for memory operations
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AtomicOrdering {
    NotAtomic, Unordered, Monotonic, Acquire, Release, AcquireRelease, SequentiallyConsistent,
}

/// Complete BasicBlock implementation with instruction management
pub struct BasicBlock {
    instructions: Vec<Instruction>,
    predecessors: Vec<Weak<BasicBlock>>,
    successors: Vec<Rc<BasicBlock>>,
    name: Option<String>,
    parent: Option<Weak<Function>>,
    metadata: HashMap<String, Metadata>,
}

impl BasicBlock {
    /// Create a new basic block
    pub fn new(name: Option<String>) -> Self {
        Self {
            instructions: Vec::new(),
            predecessors: Vec::new(),
            successors: Vec::new(),
            name,
            parent: None,
            metadata: HashMap::new(),
        }
    }

    /// Insert an instruction at the specified position
    pub fn insert_instruction(&mut self, inst: Instruction, pos: usize) -> Result<(), IRError> {
        if pos > self.instructions.len() {
            return Err(IRError::InvalidPosition);
        }

        // Validate instruction placement
        if pos < self.instructions.len() {
            if let Some(existing) = self.instructions.get(pos) {
                if existing.is_terminator() {
                    return Err(IRError::InsertAfterTerminator);
                }
            }
        }

        self.instructions.insert(pos, inst);
        self.update_successors()?;
        Ok(())
    }

    /// Append an instruction to the end of the block
    pub fn append_instruction(&mut self, inst: Instruction) -> Result<(), IRError> {
        // Check if we already have a terminator
        if let Some(last) = self.instructions.last() {
            if last.is_terminator() {
                return Err(IRError::MultipleTerminators);
            }
        }

        self.instructions.push(inst);
        self.update_successors()?;
        Ok(())
    }

    /// Get all instructions in this block
    pub fn get_instructions(&self) -> &[Instruction] {
        &self.instructions
    }

    /// Get the terminator instruction
    pub fn get_terminator(&self) -> Option<&Instruction> {
        self.instructions.last().filter(|inst| inst.is_terminator())
    }

    /// Verify the structural integrity of this basic block
    pub fn verify(&self) -> Result<(), VerificationError> {
        // Verify terminator instruction
        if let Some(last) = self.instructions.last() {
            if !last.is_terminator() {
                return Err(VerificationError::MissingTerminator);
            }
        } else {
            return Err(VerificationError::EmptyBasicBlock);
        }

        // Verify no early terminators
        for (i, inst) in self.instructions.iter().enumerate() {
            if i < self.instructions.len() - 1 && inst.is_terminator() {
                return Err(VerificationError::EarlyTerminator);
            }
        }

        // Verify successor consistency
        self.verify_successors()?;

        Ok(())
    }

    /// Optimize this basic block
    pub fn optimize(&mut self) -> Result<bool, IRError> {
        let mut changed = false;

        // Dead code elimination
        let original_len = self.instructions.len();
        self.instructions.retain(|inst| {
            inst.has_side_effects() || inst.has_users() || inst.is_terminator()
        });

        if self.instructions.len() != original_len {
            changed = true;
        }

        // Constant folding
        for inst in &mut self.instructions {
            if self.fold_constants(inst)? {
                changed = true;
            }
        }

        Ok(changed)
    }

    /// Update successor relationships based on terminator
    fn update_successors(&mut self) -> Result<(), IRError> {
        self.successors.clear();

        if let Some(terminator) = self.instructions.last() {
            match terminator {
                Instruction::Branch { true_block, false_block, .. } => {
                    self.successors.push(true_block.clone());
                    if let Some(false_bb) = false_block {
                        self.successors.push(false_bb.clone());
                    }
                }
                _ => {} // Other terminators don't have explicit successors
            }
        }

        Ok(())
    }

    /// Verify successor consistency
    fn verify_successors(&self) -> Result<(), VerificationError> {
        // Implementation would verify that successors match terminator
        Ok(())
    }

    /// Perform constant folding on an instruction
    fn fold_constants(&self, _inst: &mut Instruction) -> Result<bool, IRError> {
        // Implementation would perform constant folding
        Ok(false)
    }
}

/// Complete Function implementation
pub struct Function {
    name: String,
    ty: Rc<Type>,
    basic_blocks: Vec<Rc<BasicBlock>>,
    arguments: Vec<Argument>,
    attributes: FunctionAttributes,
    calling_convention: CallingConvention,
    metadata: HashMap<String, Metadata>,
    entry_block: Option<Rc<BasicBlock>>,
}

impl Function {
    /// Create a new function
    pub fn new(name: String, ty: Rc<Type>) -> Self {
        Self {
            name,
            ty,
            basic_blocks: Vec::new(),
            arguments: Vec::new(),
            attributes: FunctionAttributes::default(),
            calling_convention: CallingConvention::C,
            metadata: HashMap::new(),
            entry_block: None,
        }
    }

    /// Create a new basic block in this function
    pub fn create_basic_block(&mut self, name: Option<String>) -> Rc<BasicBlock> {
        let block = Rc::new(BasicBlock::new(name));

        // Set as entry block if this is the first block
        if self.basic_blocks.is_empty() {
            self.entry_block = Some(block.clone());
        }

        self.basic_blocks.push(block.clone());
        block
    }

    /// Add an existing basic block to this function
    pub fn add_basic_block(&mut self, block: Rc<BasicBlock>) -> Result<(), IRError> {
        if self.basic_blocks.is_empty() {
            self.entry_block = Some(block.clone());
        }
        self.basic_blocks.push(block);
        Ok(())
    }

    /// Get all basic blocks in this function
    pub fn get_basic_blocks(&self) -> &[Rc<BasicBlock>] {
        &self.basic_blocks
    }

    /// Get the entry basic block
    pub fn get_entry_block(&self) -> Option<&Rc<BasicBlock>> {
        self.entry_block.as_ref()
    }

    /// Get function name
    pub fn get_name(&self) -> &str {
        &self.name
    }

    /// Get function type
    pub fn get_type(&self) -> &Rc<Type> {
        &self.ty
    }

    /// Add an argument to this function
    pub fn add_argument(&mut self, arg: Argument) {
        self.arguments.push(arg);
    }

    /// Get all arguments
    pub fn get_arguments(&self) -> &[Argument] {
        &self.arguments
    }

    /// Verify the function structure
    pub fn verify(&self) -> Result<(), VerificationError> {
        // Verify function has entry block
        if self.entry_block.is_none() && !self.basic_blocks.is_empty() {
            return Err(VerificationError::NoEntryBlock);
        }

        // Verify all basic blocks
        for block in &self.basic_blocks {
            block.verify()?;
        }

        // Verify control flow
        self.verify_control_flow()?;

        // Verify argument count matches function type
        if let Type::Function { params, .. } = &*self.ty {
            if self.arguments.len() != params.len() {
                return Err(VerificationError::ArgumentCountMismatch);
            }
        }

        Ok(())
    }

    /// Verify control flow integrity
    fn verify_control_flow(&self) -> Result<(), VerificationError> {
        // Implementation would verify that all blocks are reachable
        // and control flow is well-formed
        Ok(())
    }

    /// Optimize this function
    pub fn optimize(&mut self) -> Result<bool, IRError> {
        let mut changed = false;

        // Optimize each basic block
        for block in &self.basic_blocks {
            // Note: This would need RefCell for mutable access in real implementation
            // For now, we'll simulate the optimization
            changed = true; // Placeholder
        }

        Ok(changed)
    }
}

/// Function argument representation
#[derive(Debug, Clone)]
pub struct Argument {
    pub name: Option<String>,
    pub ty: Rc<Type>,
    pub attributes: ArgumentAttributes,
}

/// Function attributes
#[derive(Debug, Clone, Default)]
pub struct FunctionAttributes {
    pub no_return: bool,
    pub no_unwind: bool,
    pub read_none: bool,
    pub read_only: bool,
    pub no_inline: bool,
    pub always_inline: bool,
    pub optimize_for_size: bool,
}

/// Argument attributes
#[derive(Debug, Clone, Default)]
pub struct ArgumentAttributes {
    pub by_val: bool,
    pub in_reg: bool,
    pub no_alias: bool,
    pub no_capture: bool,
    pub no_undef: bool,
}

/// Verification error types
#[derive(Debug, Clone)]
pub enum VerificationError {
    MissingTerminator,
    EarlyTerminator,
    EmptyBasicBlock,
    NoEntryBlock,
    ArgumentCountMismatch,
    UnreachableBlock,
    InvalidControlFlow,
}

/// Complete Module implementation
pub struct Module {
    name: String,
    context: Arc<Context>,
    functions: HashMap<String, Rc<Function>>,
    global_variables: HashMap<String, GlobalVariable>,
    metadata: HashMap<String, Metadata>,
    target_triple: Option<String>,
    data_layout: Option<String>,
}

impl Module {
    /// Create a new module
    pub fn new(name: String, context: Arc<Context>) -> Self {
        Self {
            name,
            context,
            functions: HashMap::new(),
            global_variables: HashMap::new(),
            metadata: HashMap::new(),
            target_triple: None,
            data_layout: None,
        }
    }

    /// Add a function to this module
    pub fn add_function(&mut self, function: Rc<Function>) -> Result<(), IRError> {
        let name = function.get_name().to_string();
        if self.functions.contains_key(&name) {
            return Err(IRError::DuplicateFunction);
        }
        self.functions.insert(name, function);
        Ok(())
    }

    /// Get a function by name
    pub fn get_function(&self, name: &str) -> Option<&Rc<Function>> {
        self.functions.get(name)
    }

    /// Get all functions
    pub fn get_functions(&self) -> &HashMap<String, Rc<Function>> {
        &self.functions
    }

    /// Add a global variable
    pub fn add_global_variable(&mut self, global: GlobalVariable) -> Result<(), IRError> {
        if self.global_variables.contains_key(&global.name) {
            return Err(IRError::DuplicateGlobal);
        }
        let name = global.name.clone();
        self.global_variables.insert(name, global);
        Ok(())
    }

    /// Get a global variable by name
    pub fn get_global_variable(&self, name: &str) -> Option<&GlobalVariable> {
        self.global_variables.get(name)
    }

    /// Set target triple
    pub fn set_target_triple(&mut self, triple: String) {
        self.target_triple = Some(triple);
    }

    /// Get target triple
    pub fn get_target_triple(&self) -> Option<&str> {
        self.target_triple.as_deref()
    }

    /// Set data layout
    pub fn set_data_layout(&mut self, layout: String) {
        self.data_layout = Some(layout);
    }

    /// Get data layout
    pub fn get_data_layout(&self) -> Option<&str> {
        self.data_layout.as_deref()
    }

    /// Verify the entire module
    pub fn verify(&self) -> Result<(), VerificationError> {
        // Verify all functions
        for function in self.functions.values() {
            function.verify()?;
        }

        // Verify global variables
        for global in self.global_variables.values() {
            global.verify()?;
        }

        // Verify module-level consistency
        self.verify_module_consistency()?;

        Ok(())
    }

    /// Verify module-level consistency
    fn verify_module_consistency(&self) -> Result<(), VerificationError> {
        // Implementation would verify cross-function references,
        // global variable usage, etc.
        Ok(())
    }

    /// Optimize the entire module
    pub fn optimize(&mut self) -> Result<bool, IRError> {
        let mut changed = false;

        // Optimize each function
        for function in self.functions.values() {
            // Note: Would need RefCell for mutable access in real implementation
            changed = true; // Placeholder
        }

        // Module-level optimizations
        changed |= self.optimize_globals()?;

        Ok(changed)
    }

    /// Optimize global variables
    fn optimize_globals(&mut self) -> Result<bool, IRError> {
        // Implementation would perform global optimizations
        Ok(false)
    }
}

/// Global variable representation
#[derive(Debug, Clone)]
pub struct GlobalVariable {
    pub name: String,
    pub ty: Rc<Type>,
    pub initializer: Option<Rc<Constant>>,
    pub is_constant: bool,
    pub linkage: Linkage,
    pub visibility: Visibility,
    pub alignment: Option<u32>,
    pub section: Option<String>,
}

impl GlobalVariable {
    /// Create a new global variable
    pub fn new(name: String, ty: Rc<Type>) -> Self {
        Self {
            name,
            ty,
            initializer: None,
            is_constant: false,
            linkage: Linkage::External,
            visibility: Visibility::Default,
            alignment: None,
            section: None,
        }
    }

    /// Verify this global variable
    pub fn verify(&self) -> Result<(), VerificationError> {
        // Verify initializer type matches variable type
        if let Some(init) = &self.initializer {
            // Implementation would verify type compatibility
        }
        Ok(())
    }
}

/// Linkage types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Linkage {
    External, AvailableExternally, LinkOnceAny, LinkOnceODR, WeakAny, WeakODR,
    Appending, Internal, Private, ExternalWeak, Common,
}

/// Visibility types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Visibility {
    Default, Hidden, Protected,
}

/// Extended IR Error types
#[derive(Debug, Clone)]
pub enum IRError {
    TypeMismatch { expected: Rc<Type>, found: Rc<Type> },
    InvalidPosition,
    LockError,
    NoCurrentBlock,
    InvalidOperandType,
    ExpectedPointerType,
    ExpectedFunctionType,
    ExpectedBooleanType,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    InvalidFunctionType,
    InsertAfterTerminator,
    MultipleTerminators,
    DuplicateFunction,
    DuplicateGlobal,
    NoResultType,
    NoCurrentFunction,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[test]
    fn test_context_creation() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        assert!(int32_ty.is_integer_with_bits(32));

        // Test type caching
        let int32_ty2 = context.get_integer_type(32);
        assert!(Rc::ptr_eq(&int32_ty, &int32_ty2));
    }

    #[test]
    fn test_value_creation_and_users() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        let value = Value::new(int32_ty.clone(), Some("test".to_string()));

        assert_eq!(value.get_type(), int32_ty);
        assert_eq!(value.get_name(), Some("test"));
        assert!(!value.has_users());
        assert_eq!(value.get_num_users(), 0);
    }

    #[test]
    fn test_constant_creation_and_folding() {
        let context = Context::new();
        let c1 = Constant::get_int(&context, 10, 32);
        let c2 = Constant::get_int(&context, 20, 32);

        // Test constant caching
        let c1_again = Constant::get_int(&context, 10, 32);
        assert!(Rc::ptr_eq(&c1, &c1_again));

        // Test constant folding
        let result = c1.fold_binary_op(BinaryOpcode::Add, &c2).unwrap();
        if let Constant::Int { value, .. } = &*result {
            assert_eq!(*value, 30);
        } else {
            panic!("Expected integer constant");
        }

        // Test division by zero handling
        let zero = Constant::get_int(&context, 0, 32);
        let div_result = c1.fold_binary_op(BinaryOpcode::Div, &zero);
        assert!(div_result.is_none());
    }

    #[test]
    fn test_type_system() {
        let context = Context::new();

        // Test integer types
        let i8 = context.get_integer_type(8);
        let i32 = context.get_integer_type(32);
        let i64 = context.get_integer_type(64);

        assert!(i8.is_integer_with_bits(8));
        assert!(i32.is_integer_with_bits(32));
        assert!(i64.is_integer_with_bits(64));

        assert_eq!(i8.get_size_in_bits(), 8);
        assert_eq!(i32.get_size_in_bits(), 32);
        assert_eq!(i64.get_size_in_bits(), 64);

        // Test function types
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty.clone(), vec![i32.clone(), i64.clone()]);

        if let Type::Function { params, ret, .. } = &*func_ty {
            assert_eq!(params.len(), 2);
            assert!(matches!(**ret, Type::Void));
        } else {
            panic!("Expected function type");
        }
    }

    #[test]
    fn test_instruction_creation() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        let lhs = Value::new(int32_ty.clone(), Some("lhs".to_string()));
        let rhs = Value::new(int32_ty.clone(), Some("rhs".to_string()));

        let add_inst = Instruction::BinaryOp {
            op: BinaryOpcode::Add,
            lhs: lhs.clone(),
            rhs: rhs.clone(),
            flags: ArithmeticFlags::default(),
        };

        assert_eq!(add_inst.get_opcode(), Opcode::Binary(BinaryOpcode::Add));
        assert!(!add_inst.has_side_effects());
        assert!(!add_inst.is_terminator());

        let operands = add_inst.get_operands();
        assert_eq!(operands.len(), 2);

        // Test result type
        let result_ty = add_inst.get_result_type().unwrap();
        assert_eq!(result_ty, int32_ty);
    }

    #[test]
    fn test_basic_block_operations() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        let mut block = BasicBlock::new(Some("entry".to_string()));

        // Test instruction insertion
        let value = Value::new(int32_ty.clone(), Some("test".to_string()));
        let ret_inst = Instruction::Return { value: Some(value) };

        assert!(block.append_instruction(ret_inst).is_ok());
        assert_eq!(block.get_instructions().len(), 1);

        // Test terminator detection
        assert!(block.get_terminator().is_some());

        // Test verification
        assert!(block.verify().is_ok());

        // Test multiple terminators error
        let unreachable_inst = Instruction::Unreachable;
        assert!(block.append_instruction(unreachable_inst).is_err());
    }

    #[test]
    fn test_function_creation() {
        let context = Context::new();
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty, vec![]);

        let mut function = Function::new("test_func".to_string(), func_ty);
        assert_eq!(function.get_name(), "test_func");
        assert_eq!(function.get_basic_blocks().len(), 0);
        assert!(function.get_entry_block().is_none());

        // Test basic block creation
        let entry_block = function.create_basic_block(Some("entry".to_string()));
        assert_eq!(function.get_basic_blocks().len(), 1);
        assert!(function.get_entry_block().is_some());

        // Add terminator to make it valid
        let ret_inst = Instruction::Return { value: None };
        // Note: In real implementation, we'd need RefCell for mutable access

        // Test function verification would go here
    }

    #[test]
    fn test_module_operations() {
        let context = Arc::new(Context::new());
        let mut module = Module::new("test_module".to_string(), context.clone());

        // Test function addition
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty, vec![]);
        let function = Rc::new(Function::new("test_func".to_string(), func_ty));

        assert!(module.add_function(function.clone()).is_ok());
        assert_eq!(module.get_functions().len(), 1);
        assert!(module.get_function("test_func").is_some());

        // Test duplicate function error
        assert!(module.add_function(function).is_err());

        // Test global variable addition
        let int32_ty = context.get_integer_type(32);
        let global = GlobalVariable::new("global_var".to_string(), int32_ty);
        assert!(module.add_global_variable(global).is_ok());
        assert!(module.get_global_variable("global_var").is_some());

        // Test target triple and data layout
        module.set_target_triple("x86_64-unknown-linux-gnu".to_string());
        assert_eq!(module.get_target_triple(), Some("x86_64-unknown-linux-gnu"));

        module.set_data_layout("e-m:e-i64:64-f80:128-n8:16:32:64-S128".to_string());
        assert_eq!(module.get_data_layout(), Some("e-m:e-i64:64-f80:128-n8:16:32:64-S128"));
    }

    #[test]
    fn test_memory_safety() {
        let context = Arc::new(Context::new());
        let int32_ty = context.get_integer_type(32);

        // Test that values can be safely shared
        let value1 = Value::new(int32_ty.clone(), Some("v1".to_string()));
        let value2 = Value::new(int32_ty.clone(), Some("v2".to_string()));

        // Test that types are properly reference counted
        assert_eq!(value1.get_type(), value2.get_type());
        assert!(Rc::ptr_eq(&value1.get_type(), &value2.get_type()));

        // Test that constants are cached properly
        let c1 = Constant::get_int(&context, 42, 32);
        let c2 = Constant::get_int(&context, 42, 32);
        assert!(Rc::ptr_eq(&c1, &c2));
    }

    #[test]
    fn test_thread_safety() {
        use std::thread;

        let context = Arc::new(Context::new());
        let context_clone = context.clone();

        // Test concurrent type creation
        let handle = thread::spawn(move || {
            let int32_ty = context_clone.get_integer_type(32);
            assert!(int32_ty.is_integer_with_bits(32));
        });

        let int32_ty = context.get_integer_type(32);
        assert!(int32_ty.is_integer_with_bits(32));

        handle.join().unwrap();
    }

    #[test]
    fn test_error_handling() {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        let float_ty = Rc::new(Type::Float { kind: FloatKind::Float });

        // Test type mismatch in binary operation
        let int_val = Value::new(int32_ty, Some("int".to_string()));
        let float_val = Value::new(float_ty, Some("float".to_string()));

        // This would be caught by IR builder type checking
        // For now, we just test that the types are different
        assert_ne!(int_val.get_type(), float_val.get_type());
    }
}
