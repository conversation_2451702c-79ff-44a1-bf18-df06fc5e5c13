use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use rust_pure_ir::*;
use std::sync::Arc;

fn benchmark_context_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("context_operations");
    
    group.bench_function("create_context", |b| {
        b.iter(|| {
            let context = Context::new();
            black_box(context);
        });
    });
    
    group.bench_function("get_integer_type", |b| {
        let context = Context::new();
        b.iter(|| {
            let ty = context.get_integer_type(black_box(32));
            black_box(ty);
        });
    });
    
    group.bench_function("get_function_type", |b| {
        let context = Context::new();
        let int32_ty = context.get_integer_type(32);
        let void_ty = Rc::new(Type::Void);
        b.iter(|| {
            let func_ty = context.get_function_type(
                black_box(void_ty.clone()), 
                black_box(vec![int32_ty.clone(), int32_ty.clone()])
            );
            black_box(func_ty);
        });
    });
    
    group.finish();
}

fn benchmark_constant_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("constant_operations");
    
    let context = Context::new();
    
    group.bench_function("create_int_constant", |b| {
        b.iter(|| {
            let constant = Constant::get_int(&context, black_box(42), black_box(32));
            black_box(constant);
        });
    });
    
    group.bench_function("constant_folding_add", |b| {
        let c1 = Constant::get_int(&context, 10, 32);
        let c2 = Constant::get_int(&context, 20, 32);
        b.iter(|| {
            let result = c1.fold_binary_op(black_box(BinaryOpcode::Add), black_box(&c2));
            black_box(result);
        });
    });
    
    group.bench_function("constant_folding_mul", |b| {
        let c1 = Constant::get_int(&context, 7, 32);
        let c2 = Constant::get_int(&context, 6, 32);
        b.iter(|| {
            let result = c1.fold_binary_op(black_box(BinaryOpcode::Mul), black_box(&c2));
            black_box(result);
        });
    });
    
    group.finish();
}

fn benchmark_value_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("value_operations");
    
    let context = Context::new();
    let int32_ty = context.get_integer_type(32);
    
    group.bench_function("create_value", |b| {
        b.iter(|| {
            let value = Value::new(
                black_box(int32_ty.clone()), 
                black_box(Some("test".to_string()))
            );
            black_box(value);
        });
    });
    
    group.bench_function("get_value_type", |b| {
        let value = Value::new(int32_ty.clone(), Some("test".to_string()));
        b.iter(|| {
            let ty = value.get_type();
            black_box(ty);
        });
    });
    
    group.bench_function("check_value_users", |b| {
        let value = Value::new(int32_ty.clone(), Some("test".to_string()));
        b.iter(|| {
            let has_users = value.has_users();
            black_box(has_users);
        });
    });
    
    group.finish();
}

fn benchmark_instruction_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("instruction_operations");
    
    let context = Context::new();
    let int32_ty = context.get_integer_type(32);
    let lhs = Value::new(int32_ty.clone(), Some("lhs".to_string()));
    let rhs = Value::new(int32_ty.clone(), Some("rhs".to_string()));
    
    group.bench_function("create_binary_instruction", |b| {
        b.iter(|| {
            let inst = Instruction::BinaryOp {
                op: black_box(BinaryOpcode::Add),
                lhs: black_box(lhs.clone()),
                rhs: black_box(rhs.clone()),
                flags: ArithmeticFlags::default(),
            };
            black_box(inst);
        });
    });
    
    group.bench_function("get_instruction_opcode", |b| {
        let inst = Instruction::BinaryOp {
            op: BinaryOpcode::Add,
            lhs: lhs.clone(),
            rhs: rhs.clone(),
            flags: ArithmeticFlags::default(),
        };
        b.iter(|| {
            let opcode = inst.get_opcode();
            black_box(opcode);
        });
    });
    
    group.bench_function("check_side_effects", |b| {
        let inst = Instruction::BinaryOp {
            op: BinaryOpcode::Add,
            lhs: lhs.clone(),
            rhs: rhs.clone(),
            flags: ArithmeticFlags::default(),
        };
        b.iter(|| {
            let has_side_effects = inst.has_side_effects();
            black_box(has_side_effects);
        });
    });
    
    group.bench_function("get_instruction_operands", |b| {
        let inst = Instruction::BinaryOp {
            op: BinaryOpcode::Add,
            lhs: lhs.clone(),
            rhs: rhs.clone(),
            flags: ArithmeticFlags::default(),
        };
        b.iter(|| {
            let operands = inst.get_operands();
            black_box(operands);
        });
    });
    
    group.finish();
}

fn benchmark_basic_block_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("basic_block_operations");
    
    let context = Context::new();
    let int32_ty = context.get_integer_type(32);
    
    group.bench_function("create_basic_block", |b| {
        b.iter(|| {
            let block = BasicBlock::new(black_box(Some("test".to_string())));
            black_box(block);
        });
    });
    
    group.bench_function("append_instruction", |b| {
        let mut block = BasicBlock::new(Some("test".to_string()));
        let value = Value::new(int32_ty.clone(), Some("val".to_string()));
        b.iter(|| {
            let inst = Instruction::Return { value: Some(value.clone()) };
            // Note: This would modify the block, so in real benchmark we'd need to reset
            // For now, we just measure instruction creation
            black_box(inst);
        });
    });
    
    group.bench_function("verify_basic_block", |b| {
        let mut block = BasicBlock::new(Some("test".to_string()));
        let value = Value::new(int32_ty.clone(), Some("val".to_string()));
        let inst = Instruction::Return { value: Some(value) };
        let _ = block.append_instruction(inst);
        
        b.iter(|| {
            let result = block.verify();
            black_box(result);
        });
    });
    
    group.finish();
}

fn benchmark_function_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("function_operations");
    
    let context = Context::new();
    let void_ty = Rc::new(Type::Void);
    let int32_ty = context.get_integer_type(32);
    let func_ty = context.get_function_type(void_ty, vec![int32_ty.clone()]);
    
    group.bench_function("create_function", |b| {
        b.iter(|| {
            let function = Function::new(
                black_box("test_func".to_string()), 
                black_box(func_ty.clone())
            );
            black_box(function);
        });
    });
    
    group.bench_function("create_basic_block_in_function", |b| {
        let mut function = Function::new("test_func".to_string(), func_ty.clone());
        b.iter(|| {
            let block = function.create_basic_block(black_box(Some("entry".to_string())));
            black_box(block);
        });
    });
    
    group.bench_function("add_function_argument", |b| {
        let mut function = Function::new("test_func".to_string(), func_ty.clone());
        b.iter(|| {
            let arg = Argument {
                name: black_box(Some("arg".to_string())),
                ty: black_box(int32_ty.clone()),
                attributes: ArgumentAttributes::default(),
            };
            function.add_argument(arg);
        });
    });
    
    group.finish();
}

fn benchmark_module_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("module_operations");
    
    let context = Arc::new(Context::new());
    
    group.bench_function("create_module", |b| {
        b.iter(|| {
            let module = Module::new(
                black_box("test_module".to_string()), 
                black_box(context.clone())
            );
            black_box(module);
        });
    });
    
    group.bench_function("add_function_to_module", |b| {
        let mut module = Module::new("test_module".to_string(), context.clone());
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty, vec![]);
        
        b.iter(|| {
            let function = Function::new(
                black_box(format!("func_{}", rand::random::<u32>())), 
                black_box(func_ty.clone())
            );
            let result = module.add_function(Rc::new(function));
            black_box(result);
        });
    });
    
    group.bench_function("get_function_from_module", |b| {
        let mut module = Module::new("test_module".to_string(), context.clone());
        let void_ty = Rc::new(Type::Void);
        let func_ty = context.get_function_type(void_ty, vec![]);
        let function = Function::new("test_func".to_string(), func_ty);
        let _ = module.add_function(Rc::new(function));
        
        b.iter(|| {
            let func = module.get_function(black_box("test_func"));
            black_box(func);
        });
    });
    
    group.finish();
}

fn benchmark_type_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("type_operations");
    
    let context = Context::new();
    
    for bits in [8, 16, 32, 64].iter() {
        group.bench_with_input(BenchmarkId::new("get_integer_type", bits), bits, |b, &bits| {
            b.iter(|| {
                let ty = context.get_integer_type(black_box(bits));
                black_box(ty);
            });
        });
    }
    
    group.bench_function("type_size_calculation", |b| {
        let int32_ty = context.get_integer_type(32);
        b.iter(|| {
            let size = int32_ty.get_size_in_bits();
            black_box(size);
        });
    });
    
    group.bench_function("type_alignment_calculation", |b| {
        let int32_ty = context.get_integer_type(32);
        b.iter(|| {
            let alignment = int32_ty.get_alignment();
            black_box(alignment);
        });
    });
    
    group.finish();
}

fn benchmark_memory_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_operations");
    
    let context = Context::new();
    let int32_ty = context.get_integer_type(32);
    let ptr_ty = Rc::new(Type::Pointer { 
        pointee: Box::new((*int32_ty).clone()), 
        address_space: 0 
    });
    
    group.bench_function("create_alloca", |b| {
        b.iter(|| {
            let inst = Instruction::Alloca {
                ty: black_box(int32_ty.clone()),
                array_size: None,
                alignment: 4,
            };
            black_box(inst);
        });
    });
    
    group.bench_function("create_load", |b| {
        let ptr_value = Value::new(ptr_ty.clone(), Some("ptr".to_string()));
        b.iter(|| {
            let inst = Instruction::Load {
                ptr: black_box(ptr_value.clone()),
                alignment: 4,
                volatile: false,
                ordering: AtomicOrdering::NotAtomic,
                metadata: HashMap::new(),
            };
            black_box(inst);
        });
    });
    
    group.bench_function("create_store", |b| {
        let ptr_value = Value::new(ptr_ty.clone(), Some("ptr".to_string()));
        let value = Value::new(int32_ty.clone(), Some("value".to_string()));
        b.iter(|| {
            let inst = Instruction::Store {
                value: black_box(value.clone()),
                ptr: black_box(ptr_value.clone()),
                alignment: 4,
                volatile: false,
                ordering: AtomicOrdering::NotAtomic,
            };
            black_box(inst);
        });
    });
    
    group.finish();
}

criterion_group!(
    benches,
    benchmark_context_operations,
    benchmark_constant_operations,
    benchmark_value_operations,
    benchmark_instruction_operations,
    benchmark_basic_block_operations,
    benchmark_function_operations,
    benchmark_module_operations,
    benchmark_type_operations,
    benchmark_memory_operations
);

criterion_main!(benches);
