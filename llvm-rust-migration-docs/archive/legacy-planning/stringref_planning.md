# StringRef Migration Planning Document

## 🎯 **Strategic Design Overview**

**Objective**: Create a high-performance Rust StringRef implementation that delivers 3-8x performance improvements while maintaining 100% API compatibility with LLVM's existing StringRef.

**Design Philosophy**: 
- **Performance-First**: SIMD optimization for all critical operations
- **Zero-Copy**: Maintain non-owning reference semantics
- **Drop-in Replacement**: Seamless integration with existing C++ code
- **Memory Safety**: Rust safety guarantees without runtime cost

## 🏗️ **Architecture Design**

### **Core Structure**
```rust
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct RustStringRef {
    data: *const u8,
    len: usize,
}

// Safety: RustStringRef is a non-owning reference, safe to send between threads
unsafe impl Send for RustStringRef {}
unsafe impl Sync for RustStringRef {}
```

### **Performance-Optimized Method Hierarchy**
```rust
impl RustStringRef {
    // Tier 1: SIMD-optimized critical operations
    pub fn find(&self, needle: &str) -> Option<usize> {
        match needle.len() {
            0 => Some(0),
            1 => self.find_char_simd(needle.as_bytes()[0]),
            2..=16 => self.find_short_simd(needle),
            _ => self.find_long_simd(needle),
        }
    }
    
    // Tier 2: Specialized implementations by input size
    #[cfg(target_feature = "avx2")]
    fn find_char_simd(&self, needle: u8) -> Option<usize> {
        // AVX2 parallel character search - 8x faster
    }
    
    #[cfg(target_feature = "sse4.2")]
    fn find_short_simd(&self, needle: &str) -> Option<usize> {
        // SSE4.2 string comparison instructions - 4x faster
    }
    
    // Tier 3: Fallback optimized implementations
    fn find_optimized(&self, needle: &str) -> Option<usize> {
        // Two-Way algorithm or optimized Boyer-Moore
    }
}
```

## 🚀 **SIMD Optimization Strategy**

### **1. Character Search Optimization**
```rust
#[cfg(target_feature = "avx2")]
fn find_char_avx2(&self, needle: u8) -> Option<usize> {
    use std::arch::x86_64::*;
    
    unsafe {
        let needle_vec = _mm256_set1_epi8(needle as i8);
        let haystack = self.as_bytes();
        
        for chunk_start in (0..haystack.len()).step_by(32) {
            let chunk_end = std::cmp::min(chunk_start + 32, haystack.len());
            let chunk = &haystack[chunk_start..chunk_end];
            
            if chunk.len() == 32 {
                let data = _mm256_loadu_si256(chunk.as_ptr() as *const __m256i);
                let cmp = _mm256_cmpeq_epi8(data, needle_vec);
                let mask = _mm256_movemask_epi8(cmp);
                
                if mask != 0 {
                    return Some(chunk_start + mask.trailing_zeros() as usize);
                }
            } else {
                // Handle partial chunk with scalar fallback
                return self.find_char_scalar(needle, chunk_start);
            }
        }
        None
    }
}
```

### **2. String Search with SSE4.2**
```rust
#[cfg(target_feature = "sse4.2")]
fn find_string_sse42(&self, needle: &str) -> Option<usize> {
    use std::arch::x86_64::*;
    
    if needle.len() > 16 {
        return self.find_long_optimized(needle);
    }
    
    unsafe {
        let needle_vec = _mm_loadu_si128(needle.as_ptr() as *const __m128i);
        let haystack = self.as_bytes();
        
        for i in (0..=haystack.len().saturating_sub(needle.len())).step_by(16) {
            let data = _mm_loadu_si128(haystack[i..].as_ptr() as *const __m128i);
            
            // Use PCMPESTRI for string comparison
            let result = _mm_cmpestri(
                needle_vec, needle.len() as i32,
                data, std::cmp::min(16, haystack.len() - i) as i32,
                _SIDD_CMP_EQUAL_ORDERED
            );
            
            if result < 16 {
                return Some(i + result as usize);
            }
        }
        None
    }
}
```

### **3. Case-Insensitive SIMD Operations**
```rust
#[cfg(target_feature = "avx2")]
fn find_insensitive_avx2(&self, needle: &str) -> Option<usize> {
    // ASCII-optimized case-insensitive search
    // Convert to lowercase using SIMD, then search
    
    unsafe {
        let lower_a = _mm256_set1_epi8(b'a' as i8);
        let upper_z = _mm256_set1_epi8(b'z' as i8);
        let case_diff = _mm256_set1_epi8(32); // 'a' - 'A'
        
        // Process 32 bytes at a time, converting to lowercase
        // Then use optimized search on normalized data
    }
}
```

## 🔧 **FFI Integration Layer**

### **C-Compatible Interface**
```rust
// ffi.rs
use std::os::raw::{c_char, c_int};

#[repr(C)]
pub struct CStringRef {
    data: *const c_char,
    len: usize,
}

#[no_mangle]
pub extern "C" fn rust_stringref_create(data: *const c_char, len: usize) -> CStringRef {
    CStringRef { data, len }
}

#[no_mangle]
pub extern "C" fn rust_stringref_find(
    sr: CStringRef,
    needle: *const c_char,
    needle_len: usize,
) -> c_int {
    let rust_sr = RustStringRef {
        data: sr.data as *const u8,
        len: sr.len,
    };
    
    let needle_slice = unsafe {
        std::slice::from_raw_parts(needle as *const u8, needle_len)
    };
    
    match std::str::from_utf8(needle_slice) {
        Ok(needle_str) => match rust_sr.find(needle_str) {
            Some(pos) => pos as c_int,
            None => -1,
        },
        Err(_) => -1,
    }
}

#[no_mangle]
pub extern "C" fn rust_stringref_find_insensitive(
    sr: CStringRef,
    needle: *const c_char,
    needle_len: usize,
) -> c_int {
    // Similar implementation with case-insensitive search
}
```

### **C++ Integration Wrapper**
```cpp
// StringRefRust.h
#ifndef LLVM_SUPPORT_STRINGREF_RUST_H
#define LLVM_SUPPORT_STRINGREF_RUST_H

#include "llvm/ADT/StringRef.h"

#ifdef LLVM_USE_RUST_STRINGREF

extern "C" {
    struct CStringRef {
        const char* data;
        size_t len;
    };
    
    CStringRef rust_stringref_create(const char* data, size_t len);
    int rust_stringref_find(CStringRef sr, const char* needle, size_t needle_len);
    int rust_stringref_find_insensitive(CStringRef sr, const char* needle, size_t needle_len);
}

namespace llvm {
    class StringRef {
    private:
        const char *Data = nullptr;
        size_t Length = 0;
        
        #ifdef LLVM_USE_RUST_STRINGREF
        CStringRef to_rust_stringref() const {
            return rust_stringref_create(Data, Length);
        }
        #endif
        
    public:
        // Existing constructors unchanged...
        
        size_t find(StringRef Str, size_t From = 0) const {
            #ifdef LLVM_USE_RUST_STRINGREF
            if (From == 0) {
                CStringRef rust_sr = to_rust_stringref();
                int result = rust_stringref_find(rust_sr, Str.data(), Str.size());
                return result >= 0 ? static_cast<size_t>(result) : npos;
            }
            #endif
            // Fallback to original implementation for non-zero From
            return find_original(Str, From);
        }
        
        size_t find_insensitive(StringRef Str, size_t From = 0) const {
            #ifdef LLVM_USE_RUST_STRINGREF
            if (From == 0) {
                CStringRef rust_sr = to_rust_stringref();
                int result = rust_stringref_find_insensitive(rust_sr, Str.data(), Str.size());
                return result >= 0 ? static_cast<size_t>(result) : npos;
            }
            #endif
            return find_insensitive_original(Str, From);
        }
    };
}

#endif // LLVM_USE_RUST_STRINGREF
#endif // LLVM_SUPPORT_STRINGREF_RUST_H
```

## 📊 **Performance Optimization Tiers**

### **Tier 1: Critical Path Optimizations (Week 1)**
- `find(StringRef)` - SIMD string search
- `find(char)` - Vectorized character search  
- `find_insensitive(StringRef)` - Case-insensitive SIMD search

**Expected Improvement**: 4-8x faster

### **Tier 2: High-Impact Operations (Week 2)**
- `find_first_of(StringRef)` - Character set operations
- `starts_with()` / `ends_with()` - Prefix/suffix checking
- `count(char)` - Character counting

**Expected Improvement**: 3-6x faster

### **Tier 3: Utility Operations (Week 3)**
- `compare()` / `compare_insensitive()` - String comparison
- `rfind()` variants - Reverse search operations
- `substr()` optimizations - Substring operations

**Expected Improvement**: 2-4x faster

## 🧪 **Testing Strategy**

### **Compatibility Testing**
```rust
#[cfg(test)]
mod compatibility_tests {
    use super::*;
    
    #[test]
    fn test_find_compatibility() {
        let test_cases = [
            ("hello world", "world", Some(6)),
            ("hello world", "xyz", None),
            ("", "", Some(0)),
            ("abc", "", Some(0)),
            // ... comprehensive test cases
        ];
        
        for (haystack, needle, expected) in test_cases {
            let rust_sr = RustStringRef::from_str(haystack);
            let result = rust_sr.find(needle);
            assert_eq!(result, expected, 
                "Mismatch for haystack='{}', needle='{}'", haystack, needle);
        }
    }
}
```

### **Performance Benchmarking**
```rust
#[cfg(test)]
mod benchmarks {
    use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
    
    fn benchmark_find_operations(c: &mut Criterion) {
        let mut group = c.benchmark_group("stringref_find");
        
        let test_cases = [
            ("small", "hello world", "world"),
            ("medium", &"x".repeat(1000), "xyz"),
            ("large", &"a".repeat(100000), "needle"),
        ];
        
        for (name, haystack, needle) in test_cases {
            let rust_sr = RustStringRef::from_str(haystack);
            
            group.bench_with_input(
                BenchmarkId::new("rust_simd", name),
                &(rust_sr, needle),
                |b, (sr, needle)| {
                    b.iter(|| black_box(sr.find(black_box(needle))));
                },
            );
        }
        
        group.finish();
    }
}
```

## 🎯 **Success Metrics**

### **Performance Targets**
- **String Search**: 4-8x improvement over current implementation
- **Character Search**: 2-4x improvement with SIMD
- **Case-Insensitive**: 5-10x improvement with optimized algorithms
- **Character Sets**: 4-12x improvement with parallel processing

### **Quality Targets**
- **100% API Compatibility**: All existing StringRef methods work identically
- **Memory Safety**: Zero buffer overflows or memory violations
- **Cross-Platform**: Consistent behavior on all LLVM-supported platforms
- **Unicode Correctness**: Proper handling of UTF-8 and non-ASCII text

### **Integration Targets**
- **Zero Code Changes**: Existing LLVM code works without modification
- **Build System**: Seamless CMake integration with feature flags
- **Testing**: All existing StringRef tests pass with Rust implementation

This planning document provides the detailed architecture for a high-performance StringRef migration that leverages SIMD optimization while maintaining full compatibility with LLVM's existing codebase.
